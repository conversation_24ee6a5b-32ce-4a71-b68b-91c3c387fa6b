import { Tabs, TabsProps } from 'antd';
import React, { FC } from 'react';
import { AppHeader } from 'widgets/AppHeader';
import {
  GlobalAssignModal,
  ReportsFormModal,
  PermissionDownloadModal,
  permissionModalsConfig,
  UserPermissionModal,
  RolePermissionModal,
  GroupPermissionModal,
  ProfileConfigModal,
  DateRangeModal,
  MatrixTableModal,
} from 'widgets/PermissionModals';
import { PageContainer } from 'shared/ui/PageContainer';

import { useTabsActions } from '../model';

import { GroupsTab } from './groupsTab';
import { ReportsTab } from './reportsTab';
import { RolesTab } from './rolesTab';
import styles from './styles.module.scss';
import { UsersTab } from './usersTab';

const tabs: TabsProps['items'] = [
  {
    label: permissionModalsConfig.enums.PermissionTabNames.Users,
    key: '1',
    children: <UsersTab />,
  },
  {
    label: permissionModalsConfig.enums.PermissionTabNames.Groups,
    key: '2',
    children: <GroupsTab />,
  },
  {
    label: permissionModalsConfig.enums.PermissionTabNames.Roles,
    key: '3',
    children: <RolesTab />,
  },
  {
    label: permissionModalsConfig.enums.PermissionTabNames.Report,
    key: '4',
    children: <ReportsTab />,
  },
];

export const PermissionControls: FC = () => {
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */
  const { onTabChange, currentTab } = useTabsActions();

  return (
    <PageContainer containerKey="permissionControls" className={styles.main}>
      <AppHeader title="Управление доступом" />

      <Tabs
        type="card"
        animated
        className={styles.tabs}
        activeKey={currentTab}
        onTabClick={onTabChange}
        items={tabs}
      />

      <GlobalAssignModal />
      <ReportsFormModal />
      <PermissionDownloadModal />
      <UserPermissionModal />
      <GroupPermissionModal />
      <RolePermissionModal />
      <ProfileConfigModal />
      <DateRangeModal />
      <MatrixTableModal />
    </PageContainer>
  );
};
