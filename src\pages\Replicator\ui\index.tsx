import { Calendar } from 'antd';
import moment, { Moment } from 'moment';
import type { FC } from 'react';
import { useCallback, useState } from 'react';

import {
  ReplicatorData,
  ReplicatorStore,
  ReplicatorProps,
  ReplicatorConfig,
} from 'pages/Replicator';
import { AppHeader } from 'widgets/AppHeader';

import { ReplicatorLog } from 'widgets/ReplicatorLog';
import { ReplicatorSettings } from 'widgets/ReplicatorSettings';
import {
  DEFAULT_DATE_VARIANT,
  REPLICATOR_DATE_VARIANT,
} from 'shared/config/constants';
import { usePopupsToggle } from 'shared/model';

import { ApiContainer } from 'shared/ui';
import { PageContainer } from 'shared/ui/PageContainer';
import { calendarHeader } from './CalendarHeader';
import { CatalogUploader } from './CatalogUploader';
import { cellRender } from './DateRender';
import { ProgressBar } from './ProgressBar';
import { Skeleton } from './Skeleton';

import styles from './styles.module.scss';

export const Replicator: FC<ReplicatorProps> = ({ title }) => {
  /* ----------------------------------------------------
   *                      Хуки
   ---------------------------------------------------- */

  const [{ data, isPending, error }, getData] =
    ReplicatorStore.hooks.useGetData();
  const [progressBarData, getProgressData] =
    ReplicatorStore.hooks.useGetProgressBar();
  const [popup, handlePopup] = usePopupsToggle(ReplicatorConfig.popupInitial);

  const [date, setDate] = useState<string | undefined>('');
  const [nowDate, setNowDate] = useState(moment);
  const [dateLog, setDateLog] = useState<ReplicatorData | null>(null);

  /* ----------------------------------------------------
   *                      Колбеки
   ---------------------------------------------------- */
  const handleNowDate = useCallback((value: Moment) => {
    setNowDate(value);
  }, []);

  const handleCalendarCellClick = useCallback(
    (value: moment.Moment, item: import('pages/Replicator').ReplicatorData) => {
      if (item) {
        setDateLog(item);
        setDate(value.format(DEFAULT_DATE_VARIANT));
      }

      handlePopup('log');
    },
    [popup], // eslint-disable-line
  );

  /* ----------------------------------------------------
   *                      UI
   ---------------------------------------------------- */

  return (
    <PageContainer containerKey="replicator">
      <AppHeader title={title} />

      <ProgressBar
        progressBarData={
          progressBarData || {
            isLoadingDatabase: false,
            text: '',
            current: 0,
            total: 0,
          }
        }
      />

      <ApiContainer error={error} isPending={false}>
        <div className={styles.calendar}>
          <Calendar
            disabledDate={(value) => value.month() !== nowDate.month()}
            className={styles.calendarContainer}
            dateCellRender={(value) =>
              cellRender({
                value,
                isPending,
                skeletons: <Skeleton />,
                onClick: handleCalendarCellClick,
                calendarCell: data || [],
              })
            }
            headerRender={(value) =>
              calendarHeader({
                ...value,
                handlePopup,
                handleNowDate,
                nowDate,
                getData,
              })
            }
          />
        </div>
      </ApiContainer>

      <ReplicatorSettings
        isOpened={popup.settings}
        refetch={() => {
          getProgressData();
          getData(nowDate.format(REPLICATOR_DATE_VARIANT));
        }}
        isLoading={progressBarData?.isLoadingDatabase}
        handleClose={() => handlePopup('settings')}
      />

      <ReplicatorLog
        isOpened={popup.log}
        handleClose={() => handlePopup('log')}
        log={
          dateLog || {
            content: '',
            data: { columns: [], rows: [] },
            key: '1',
            logInfo: [],
            type: 'default',
          }
        }
        date={date}
      />

      <CatalogUploader
        isOpened={popup.upload}
        onClose={() => handlePopup('upload')}
      />
    </PageContainer>
  );
};
