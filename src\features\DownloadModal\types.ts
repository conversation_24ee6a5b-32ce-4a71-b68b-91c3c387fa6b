import { specialPermissionsStore } from 'entities/SpecialPermissions';
import { DownloadMessages } from 'shared/lib';

/** Пропсы для модалки загрузки */
export interface DownloadModalProps {
  permissions: specialPermissionsStore.enums.ReportPermissions[];
  /** @link ReportControlDTO */
  reportControlDTO: ReportControlDTO;
  /** Эндпоинт для запросов в таблицу */
  url: Endpoint;
  /** Доп реквест параметры */
  additionalRequestParams?: Record<string, string>;
  /** Флаг экспорта в Excel */
  isExportToExcel?: boolean;
  /** Флаг асинхронных отчетов */
  isLazyReport?: boolean;
  onOutputOptions?: {
    /** Кастомный колбек на нажатие кнопки */
    customCallback?: (values: InputValues) => Promise<void>;
    /** Кастомные сообщения об ошибках */
    customErrorMessages?: DownloadMessages;
    /** Кастомное имя файла, по дефолту берется из заголовков респонса */
    customFileName?: string;
  };
  /** Эндпойнт отчетов, если отличается от основного эндпойнта таблицы */
  reportEndpoint?: string;
  /** Флаг пропуска проверки прав для выгрузки отчета */
  skipRightsCheck?: boolean;
  /** Опциолнальный титул */
  title?: Title;
}

export type ReportTypes = 'xlsx' | 'docx' | 'html' | 'rtf';

/** Тип для стейта */
export interface InputValues {
  format: number;
  isHighlighted: boolean;
  reportType: ReportTypes;
}

/** Пропсы подкомпонента экспорта */
export interface ExportProps {
  /** @link Callback */
  onClick: Callback;
}

/** Пропсы подкомпонента отчетов */
export type ReportsProps = {
  /** Колбек на изменение значения */
  onChange: (selectValue: InputValues['format']) => void;
  /** Текущее значение селекта */
  selectValue: InputValues['format'];
} /** Проброс пропсов урла */ & Pick<DownloadModalProps, 'url'>;
