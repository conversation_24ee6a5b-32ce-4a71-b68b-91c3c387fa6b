import { Input } from 'antd';
import { FC, useCallback, useEffect } from 'react';
import {
  permissionModalsConfig,
  permissionModalsStore,
} from 'widgets/PermissionModals';
import { DataGrid, dataGridConfig } from 'features/DataGrid';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { ApiContainer } from 'shared/ui/ApiContainer';

import styles from './styles.module.scss';

export const ReportsTab: FC = () => {
  const { rowsOnPage, currentPage } = useAppSelector(
    permissionModalsStore.selectors.reportsPaginationSelector,
  );

  const totalPages = useAppSelector(
    permissionModalsStore.selectors.reportsPageSelector,
  );
  const currentSelect = useAppSelector(
    permissionModalsStore.selectors.reportsCurrentSelectSelector,
  );
  const { columns, rows, error, isPending } = useAppSelector(
    permissionModalsStore.selectors.reportsApiSelector,
  );

  const dispatch = useAppDispatch();

  const { openModal } = useCreateSliceActions(
    permissionModalsStore.reducers.slice.actions,
  );

  const onPaginatorClick = useCallback(
    (page, size) => {
      dispatch(
        permissionModalsStore.actions.postReportsThunk({
          additionalQuery: { page, size },
        }),
      );
    },
    [dispatch],
  );

  useEffect(() => {
    if (columns.length === 0) {
      openModal(permissionModalsConfig.enums.PermissionModals.ReportsForm);
    }
  }, [columns.length, openModal]);

  return (
    <ApiContainer
      error={error}
      isPending={false}
      errorTitle="Произошла ошибка при построении отчета, попробуйте настроить заново"
      errorStatus={404}
    >
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{ show: true, label: '' }}
        resizableProps={{ isActive: true }}
        hideColumnSearch
        hideSorter
        tableAdditionProps={{
          loading: isPending,
          scroll: { x: '100%', y: 600 },
          pagination: {
            pageSize: rowsOnPage,
            pageSizeOptions: dataGridConfig.constants.PAGE_SIZE_OPTIONS,
            position: ['bottomLeft'],
            hideOnSinglePage: false,
            current: currentPage,
            onChange: onPaginatorClick,
            total: totalPages,
            onShowSizeChange: (page, size) => onPaginatorClick(page, size),
            size: 'small',
          },
        }}
        secondRowAdditionalButtons={[
          {
            key: 'openReport',
            title: 'Настроить отчет',
            type: 'primary',
            onClick: () =>
              openModal(
                permissionModalsConfig.enums.PermissionModals.ReportsForm,
              ),
          },
        ]}
        additionalComponent={
          <Input
            maxLength={4000}
            className={styles.input}
            value={`Вид отчета: ${
              columns.length > 0 ? currentSelect : 'Отчет не выбран'
            }`}
            disabled
          />
        }
      />
    </ApiContainer>
  );
};
