import { Table } from 'antd';
import { FC, useEffect } from 'react';
import {
  permissionModalsConfig,
  permissionModalsStore,
  permissionModalsLib,
} from 'widgets/PermissionModals';
import { DataGrid } from 'features/DataGrid';
import { createInputsModal } from 'features/InputsModal';
import { apiUrls } from 'shared/api';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { ApiContainer } from 'shared/ui/ApiContainer';
import { renderGroupAndRolesColumn } from '../../lib';
import { useButtonsState, useOpenConfirmWithAction } from '../../model';
import { selectors } from '../../store';

export const GroupsTab: FC = () => {
  const [selected, { handleSelection, clearSelection }] = useTableSelection();
  const isButtonDisabled = useButtonsState(selected.rows, 'isDeleted');
  const [, open] = permissionModalsStore.hooks.usePermissionModal('group');

  const { isPending, rows, columns, error, isLoaded } = useAppSelector(
    selectors.groupsTabSelector,
  );

  const dispatch = useAppDispatch();

  const { openModal } = useCreateSliceActions(
    permissionModalsStore.reducers.slice.actions,
  );

  const onRefresh = (): void => {
    dispatch(permissionModalsStore.actions.getGroupsThunk());
  };

  /* Хук на создание колбека с пендингом на рефетч */
  const [handleOpenConfirmWithAction, triggerPending] =
    useOpenConfirmWithAction(onRefresh, clearSelection);

  const additionalButtons: AdditionalButton[] = [
    {
      title: 'Назначение "многие ко многим" ',
      key: 'global',
      type: 'primary',
      onClick: () =>
        openModal(permissionModalsConfig.enums.PermissionModals.GlobalAssign),
    },

    {
      title: 'Обновить',
      key: 'refresh',
      type: 'primary',
      onClick: onRefresh,
      disabled: isPending,
    },

    {
      title: 'Новая группа',
      key: 'new-group',
      type: 'primary',
      onClick: () =>
        createInputsModal({
          additionalBodyKeys: { valid: true },
          endpoint: apiUrls.permission.groups.createUpdate,
          title: permissionModalsConfig.permissionStrings.group.newTitle,
          onSubmit: async () => {
            await dispatch(
              permissionModalsStore.actions.getGroupsThunk(),
            ).unwrap();
          },
          successMessage: `Группа успешно создана!`,
          inputs: permissionModalsLib.renderGroupInputs(),
        }),
    },

    {
      disabled: isButtonDisabled.reset,
      title: 'Восстановить',
      key: 'reset-selected',
      type: 'primary',
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.groups.restore,
          {
            title: 'Восстановление',
            subTitle: 'Вы действительно хотите восстановить выбранные группы?',
            notice: 'Группы успешно восстановлены!',
          },
          selected.rows,
        ),
    },

    {
      disabled: isButtonDisabled.delete,
      title: 'Удалить',
      key: 'delete-selected',
      danger: true,
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.groups.delete,
          {
            title: 'Удаление',
            subTitle: 'Вы действительно хотите удалить выбранные группы?',
            notice: 'Группы успешно удалены!',
          },
          selected.rows,
        ),
    },
  ];

  useEffect(() => {
    if (!isLoaded) {
      onRefresh();
    }
  }, []); // eslint-disable-line

  return (
    <ApiContainer
      error={error}
      isPending={!isLoaded && isPending}
      refresh={onRefresh}
      errorStatus={404}
    >
      <DataGrid
        columns={renderGroupAndRolesColumn(columns)}
        rows={rows}
        searchProps={{
          show: true,
          label:
            'Введите значение для поиска, например: нет ролей или полный доступ',
        }}
        paginationProps={{
          show: false,
          total: 0,
          currentPage: 0,
        }}
        filterCheckbox={permissionModalsConfig.groupsFilterCheckboxes}
        resizableProps={{ isActive: true }}
        additionalButtons={additionalButtons}
        tableAdditionProps={{
          tableLayout: 'fixed',
          loading: {
            size: 'large',
            spinning: triggerPending || (isLoaded && isPending),
          },
          size: 'small',
          scroll: {
            y: `calc(88vh - 10em)`,
            x: 'max-content',
          },
          pagination: {
            pageSize: 14,
            showSizeChanger: false,
            hideOnSinglePage: true,
            position: ['bottomLeft'],
            size: 'small',
          },
          rowSelection: {
            selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
            selectedRowKeys: selected.keys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleSelection({ keys: selectedRowKeys, rows: selectedRows });
            },
          },
        }}
        tableAdditionHandlers={{
          onRowClick: (row) => open(row.rowId?.rowId ?? ''),
        }}
      />
    </ApiContainer>
  );
};
