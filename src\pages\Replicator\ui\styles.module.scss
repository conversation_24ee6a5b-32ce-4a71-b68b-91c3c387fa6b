@import 'src/app/styles/mixins';

.calendar {
  position: relative;

  &Container {
    scrollbar-width: thin;
    @include scrollBar;

    :global(.ant-fullcalendar-cell
        .ant-fullcalendar-last-month-cell
        .ant-fullcalendar-disabled-cell-first-of-row
        .ant-fullcalendar-disabled-cell) {
      cursor: auto !important;
    }

    :global(.ant-fullcalendar-content) {
      &::-webkit-scrollbar {
        width: 7px;
        height: 1px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgb(80 80 80 / 50%);
        border-radius: 4px;
        border: 1px solid rgb(230 230 230 / 93.2%);
      }

      &::-webkit-scrollbar-button:single-button {
        background-color: rgb(255 255 255 / 8%);
        display: block;
        border-style: solid;
        height: 13px;
        width: 16px;
      }

      &::-webkit-scrollbar-button:single-button:vertical:decrement {
        border-width: 0 8px 8px;
        border-color: transparent transparent #5b5b5b;
      }

      &::-webkit-scrollbar-button:single-button:vertical:decrement:hover {
        border-color: transparent transparent #212121;
      }

      &::-webkit-scrollbar-button:single-button:vertical:increment {
        border-width: 8px 8px 0;
        border-color: #5b5b5b transparent transparent;
      }

      &::-webkit-scrollbar-button:vertical:single-button:increment:hover {
        border-color: #212121 transparent transparent;
      }
    }

    :global(.ant-fullcalendar-value) {
      font-size: 24px;
    }

    :global(.ant-fullcalendar-calendar-body) {
      height: 78vh;
      overflow: auto;
      @include scrollBar;
    }

    :global(.ant-fullcalendar-disabled-cell .ant-fullcalendar-value) {
      color: rgb(0 0 0 / 60%);
      cursor: pointer !important;
    }
  }
}
