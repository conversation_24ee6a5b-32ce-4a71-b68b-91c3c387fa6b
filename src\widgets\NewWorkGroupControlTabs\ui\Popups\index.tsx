import { FC } from 'react';
import { filtersDrawerStore } from 'widgets/FiltersDrawer';
import { NWGCConfig } from 'widgets/NewWorkGroupControlTabs';
import { WGDownloadReport } from 'widgets/WGDownloadReport';
import { WorkGroupComposition } from 'widgets/WorkGroupComposition';
import {
  WGCNotificationStore,
  WorkGroupControlNotification,
} from 'widgets/WorkGroupControlNotification';
import { TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { generateUrlWithQueryParams, lazyDownloadFile } from 'shared/lib';
import { useAppSelector } from 'shared/model';

interface PopupsProps {
  cabinetId: string;
  popups: Record<keyof typeof NWGCConfig.popupsState, boolean>;
  refetch: Callback;
  rowAdditional: { data: TableRowData; reset: Callback };
  selectedRows: TableRowData[];
  togglePopup: (name: keyof typeof NWGCConfig.popupsState) => void;
}
export const Popups: FC<PopupsProps> = ({
  cabinetId,
  refetch,
  selectedRows,
  togglePopup,
  popups,
  rowAdditional,
}) => {
  const selectedFilters = useAppSelector(
    filtersDrawerStore.selectors.selectedFiltersSelector,
  );
  return (
    <>
      <WorkGroupControlNotification
        isOpened={popups.createNotification}
        cabinetId={cabinetId}
        onClose={() => togglePopup('createNotification')}
        refetch={refetch}
        type={WGCNotificationStore.enums.PopupType.Create}
      />

      <WorkGroupControlNotification
        isOpened={popups.updateNotification}
        cabinetId={cabinetId}
        onClose={() => {
          rowAdditional.reset();
          togglePopup('updateNotification');
        }}
        refetch={refetch}
        operationTypeName={rowAdditional.data?.operationType || ''}
        params={rowAdditional.data.rowId}
        isResend={rowAdditional.data?.isResend}
        planingDate={rowAdditional.data?.planningSending || ''}
        type={WGCNotificationStore.enums.PopupType.Update}
      />

      {popups.createUser && (
        <WorkGroupComposition
          type="create"
          title="Добавление нового члена рабочей группы"
          onClose={() => togglePopup('createUser')}
          endpoint={apiUrls.workGroupControl.composition.createUser}
          cabinetId={cabinetId}
          refetch={refetch}
          showOnlyFounded={false}
        />
      )}

      {popups.updateUser && (
        <WorkGroupComposition
          type="update"
          title={`Изменение роли пользователя ${selectedRows.map(
            (item) => item.fio,
          )}`}
          onClose={() => togglePopup('updateUser')}
          endpoint={apiUrls.workGroupControl.composition.updateUser}
          cabinetId={cabinetId}
          refetch={refetch}
          rowId={selectedRows?.[0]?.rowId || {}}
        />
      )}

      <WGDownloadReport
        isOpened={popups.downloadReport}
        title="Сведения по операциям КРГ"
        params={{
          date: true,
          select: false,
          reportTypeKey: 'workgroup_cabinet_management_wgc_operations',
        }}
        onClose={() => togglePopup('downloadReport')}
        onOutputToFileClick={async (values) => {
          await lazyDownloadFile(
            selectedFilters,
            generateUrlWithQueryParams(
              apiUrls.workGroupControl.reports.fileId,
              {
                cabinetId,
                dateFrom: values.dateFrom,
                dateTo: values.dateTo,
                type: values.type,
                format: values.ext,
              },
            ),
            apiUrls.workGroupControl.reports.fileState,
            apiUrls.workGroupControl.reports.fileDownload,
            NWGCConfig.constants.MAX_FILE_CHECK_ATTEMPTS,
            NWGCConfig.constants.FILE_CHECK_DELAY,
          );
        }}
      />
    </>
  );
};
