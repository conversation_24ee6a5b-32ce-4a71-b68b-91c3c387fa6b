import { Divider, Progress, Typography } from 'antd';
import { FC } from 'react';
import { ProgressBarProps } from 'pages/Replicator';
import styles from './styles.module.scss';

export const ProgressBar: FC<ProgressBarProps> = ({ progressBarData }) =>
  progressBarData?.isLoadingDatabase && progressBarData?.text ? (
    <div className={styles.progress}>
      <div className={styles.progressText}>
        <Typography.Text>Текущая операция:</Typography.Text>
        <Typography.Text type="secondary">
          Загрузка данных в буферную схему данных (
          {progressBarData && progressBarData.text})
        </Typography.Text>
      </div>
      <Progress
        percent={
          progressBarData &&
          Math.floor((progressBarData.current / progressBarData.total) * 100)
        }
        status="active"
        className={styles.containerProgress}
      />
    </div>
  ) : (
    <Divider className={styles.divider} />
  );
