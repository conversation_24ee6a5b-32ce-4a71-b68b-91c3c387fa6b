import { useEffect } from 'react';
import { apiUrls, baseAppInstance } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { RequestStatuses, useAxiosRequest } from 'shared/model';

export const useExecutionControlPermissions = ({
  resource,
  action,
}: {
  action: string;
  resource: string;
}): RequestStatuses<{ permit: boolean }> => {
  const [getPermitRequest, permit] = useAxiosRequest<{
    permit: boolean;
  }>(baseAppInstance);

  useEffect(() => {
    const getPermit = async (): Promise<void> => {
      await getPermitRequest(
        generateUrlWithQueryParams(
          apiUrls.permission.executionControl.getPermit,
          {
            resource,
            action,
          },
        ),
      );
    };
    getPermit();
  }, [action, getPermitRequest, resource]);

  useEffect(() => {
    if (permit.isResEnd && permit.error) {
      appErrorNotification(
        'Произошла ошибка получения прав для Контроля выполнения',
        permit.error,
      );
    }
  }, [permit.isResEnd, permit.error]);

  return permit;
};
