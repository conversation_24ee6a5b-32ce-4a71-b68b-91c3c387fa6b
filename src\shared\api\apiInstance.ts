import axios, { AxiosRequestHeaders } from 'axios';
import { IS_ZT_BUILD, isDevEnv } from 'shared/config';

axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
const KZID_DEV = 'http://************'; // 'http://localhost:8080';
const MAIN_SERVICE = '/kzid_rest';
const FILENET_SERVICE = '/filenet';
const NOTIFY_SERVICE = '/notify/api/external';
const checkAndAddXPrincipal = (): { headers?: AxiosRequestHeaders } =>
  IS_ZT_BUILD
    ? {}
    : {
        headers: {
          'x-principal':
            'eyJuYW1lIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1IiwicHJpbmNpcGFsIjp7ImF1dGhvcml0aWVzIjpbeyJhdHRyaWJ1dGVzIjp7InVpZCI6ImlkdXNlcjFAcG9ydGFsLmNici5ydSIsImRuIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1IiwiZXh0ZW5zaW9uQXR0cmlidXRlMTEiOiLQk9C70LDQstC90LDRjyDQuNC90YHQv9C10LrRhtC40Y8g0JHQsNC90LrQsCDQoNC+0YHRgdC40LgiLCJleHRlbnNpb25BdHRyaWJ1dGUxMiI6ItCj0L/RgNCw0LLQu9C10L3QuNC1INC40L3RhNC+0YDQvNCw0YbQuCIsImVtYWlsIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1IiwiZXh0ZW5zaW9uQXR0cmlidXRlMTMiOiLQntGC0LTQtdC7INGE0YPQvdC60YbQuNC+0L3QsNC70YzQvdGL0YUifX1dLCJhdHRyaWJ1dGVzIjp7InVpZCI6ImlkdXNlcjFAcG9ydGFsLmNici5ydSIsImRuIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1IiwiZXh0ZW5zaW9uQXR0cmlidXRlMTEiOiLQk9C70LDQstC90LDRjyDQuNC90YHQv9C10LrRhtC40Y8g0JHQsNC90LrQsCDQoNC+0YHRgdC40LgiLCJleHRlbnNpb25BdHRyaWJ1dGUxMiI6ItCj0L/RgNCw0LLQu9C10L3QuNC1INC40L3RhNC+0YDQvNCw0YbQuCIsImVtYWlsIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1IiwiZXh0ZW5zaW9uQXR0cmlidXRlMTMiOiLQntGC0LTQtdC7INGE0YPQvdC60YbQuNC+0L3QsNC70YzQvdGL0YUifX0sImNyZWRlbnRpYWxzIjpudWxsLCJkZXRhaWxzIjpudWxsLCJhdXRob3JpdGllcyI6W3siYXR0cmlidXRlcyI6eyJ1aWQiOiJpZHVzZXIxQHBvcnRhbC5jYnIucnUiLCJkbiI6ImlkdXNlcjFAcG9ydGFsLmNici5ydSIsImV4dGVuc2lvbkF0dHJpYnV0ZTExIjoi0JPQu9Cw0LLQvdCw0Y8g0LjQvdGB0L/QtdC60YbQuNGPINCR0LDQvdC60LAg0KDQvtGB0YHQuNC4IiwiZXh0ZW5zaW9uQXR0cmlidXRlMTIiOiLQo9C/0YDQsNCy0LvQtdC90LjQtSDQuNC90YTQvtGA0LzQsNGG0LgiLCJlbWFpbCI6ImlkdXNlcjFAcG9ydGFsLmNici5ydSIsImV4dGVuc2lvbkF0dHJpYnV0ZTEzIjoi0J7RgtC00LXQuyDRhNGD0L3QutGG0LjQvtC90LDQu9GM0L3Ri9GFIn19XSwiYXV0aGVudGljYXRlZCI6bnVsbCwiYXV0aG9yaXplZENsaWVudFJlZ2lzdHJhdGlvbklkIjpudWxsLCJpZCI6MjQ5MiwiZGlzcGxheU5hbWUiOm51bGwsInVwbiI6ImlkdXNlcjFAcG9ydGFsLmNici5ydSIsImVtYWlsIjoiaWR1c2VyMUBwb3J0YWwuY2JyLnJ1Iiwiam9iVGl0bGUiOm51bGwsInRlbGVwaG9uZU51bWJlciI6bnVsbCwiZG4iOiJpZHVzZXIxQHBvcnRhbC5jYnIucnUifQ==',
        },
      };

const maksimBek = 'http://172.30.165.139:8080'; // eslint-disable-line
const maksimBekKzidBek = `${KZID_DEV}:18080`; // eslint-disable-line
const vovaBek = `${KZID_DEV}:8080`; // eslint-disable-line
const vovaLocalBek = 'http://172.30.165.47:8080'; // eslint-disable-line
const vovaKoBek = 'http://172.30.165.47:8088'; // eslint-disable-line
const vovaPlansBek = 'http://172.30.165.47:8089'; // eslint-disable-line
const vovaWGBek = 'http://172.30.165.47:8098'; // eslint-disable-line
const dimaBek = 'http://127.0.0.1:18080'; // eslint-disable-line
const dimaWGBek = 'http://172.30.166.62:8080'; // eslint-disable-line
const sashaTeamBek = 'http://172.30.165.146:8080'; // eslint-disable-line
const alexWGBek = 'http://172.30.166.139:8081'; // eslint-disable-line
const alexBek = 'http://172.30.166.139:8080'; // eslint-disable-line
const olegBek = 'http://172.30.166.58:8098'; // eslint-disable-line
const dockerBek = `${KZID_DEV}:8581`; // eslint-disable-line
const kurskyBek = `${KZID_DEV}:8297`; // eslint-disable-line
const refactorBek = `${KZID_DEV}:8098`; // eslint-disable-line
const artemNoticeBek = `${KZID_DEV}:8083`; // eslint-disable-line
const amBekFnet = `${KZID_DEV}:8086`; // eslint-disable-line
const lylovWGCBek = 'http://drpo-lylov:8297'; // eslint-disable-line
const lylovECBek = 'http://drpo-lylov:8298'; // eslint-disable-line
const lylovACBek = 'http://drpo-lylov:8299'; // eslint-disable-line
const lylovAPBek = 'http://drpo-lylov:8301'; // eslint-disable-line

/** Сюда указывать урл, к кому конектимся */
const devServerUrl = dimaBek;

export const API_FOR_DOCKER = window.location.origin;

/** Строка с url ендпоинта */
export const API_URL = isDevEnv ? devServerUrl : API_FOR_DOCKER;

/** Api инстансы */
export const baseAppInstance = axios.create({
  baseURL: IS_ZT_BUILD ? API_FOR_DOCKER : API_URL,
  ...checkAndAddXPrincipal(),
});

export const appInstance = axios.create({
  baseURL: (IS_ZT_BUILD ? API_FOR_DOCKER : API_URL) + MAIN_SERVICE,
  ...checkAndAddXPrincipal(),
});

export const filenetServiceInstance = axios.create({
  baseURL: (IS_ZT_BUILD ? API_FOR_DOCKER : API_URL) + FILENET_SERVICE,
  ...checkAndAddXPrincipal(),
});

export const filenetFiltersInstance = axios.create({
  baseURL: (IS_ZT_BUILD ? API_FOR_DOCKER : API_URL) + MAIN_SERVICE,
  ...checkAndAddXPrincipal(),
});

export const notificationsAndSubscriptionsInstance = axios.create({
  baseURL: (IS_ZT_BUILD ? API_FOR_DOCKER : API_URL) + NOTIFY_SERVICE,
  ...checkAndAddXPrincipal(),
});

export const emptyBaseUrlInstance = axios.create({});

export const appInstanceWithoutService = axios.create({
  baseURL: IS_ZT_BUILD ? API_FOR_DOCKER : API_URL,
});
