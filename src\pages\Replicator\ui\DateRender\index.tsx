import { Badge, Popover } from 'antd';
import moment, { Moment } from 'moment/moment';
import { FC, ReactElement, ReactNode } from 'react';

import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';

import styles from './styles.module.scss';

interface DateRenderProps {
  calendarCell: import('pages/Replicator').ReplicatorDate[];
  isPending: boolean;
  onClick: (
    value: moment.Moment,
    item: import('pages/Replicator').ReplicatorData,
  ) => void;
  skeletons: ReactElement;
  value: Moment;
}

const getSwitchData = (
  value: Moment,
  cell: import('pages/Replicator').ReplicatorDate[],
): import('pages/Replicator').ReplicatorData[] =>
  Array.isArray(cell)
    ? cell.reduce((arr, item) => {
        switch (value.format(DEFAULT_DATE_VARIANT)) {
          case item.day:
            arr.push(...item.data);
            break;
          default:
        }
        return arr;
      }, [] as import('pages/Replicator').ReplicatorData[])
    : [];

export const DateRender: FC<DateRenderProps> = ({
  calendarCell,
  onClick,
  skeletons,
  isPending,
  value,
}) => {
  const date = getSwitchData(value, calendarCell);
  return (
    <ul className={styles.cell}>
      {isPending
        ? skeletons
        : date.map((item) => (
            <div className={styles.cellContainer} key={item.key}>
              <Popover
                content="Нажмите для просмотра лога загрузки"
                key={item.key}
                placement="right"
              >
                <button
                  className={styles.cellButton}
                  onClick={() => onClick(value, item)}
                >
                  <Badge
                    className={styles.cellBadge}
                    status={item.type}
                    text={item.content}
                  />
                </button>
              </Popover>
            </div>
          ))}
    </ul>
  );
};

export const cellRender = (props: DateRenderProps): ReactNode => (
  <DateRender {...props} />
);
