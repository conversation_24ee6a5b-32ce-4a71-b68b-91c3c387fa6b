/* eslint-disable no-restricted-syntax */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { getDescendantNodeIds, initializeTree, processTreeData } from '../lib';
import { TreesState, TreeState } from '../types';
import { getTreeThunk } from './thunks';

const getInitialTreeNodeState = (): TreeState => ({
  entities: {},
  error: undefined,
  loading: false,
  rootId: [],
  loadedKeys: {},
  expandedKeys: {},
  checkedKeys: {},
  loadingKeys: {},
  selectedNodeId: '',
});

const initialState: TreesState = { trees: {} };

export const slice = createSlice({
  name: 'newLazyTrees',
  initialState,
  reducers: {
    initializeTreeWithData: (
      state,
      {
        payload,
      }: PayloadAction<{
        treeData: TreeElement[];
        treeKey: string;
      }>,
    ) => {
      const { treeKey, treeData } = payload;
      initializeTree(state, treeKey, getInitialTreeNodeState());
      state.trees[treeKey]!.loading = false;
      processTreeData(treeData, state.trees[treeKey]!);
    },
    deleteNode: (
      state,
      {
        payload,
      }: PayloadAction<{
        itemId: string;
        treeKey: string;
      }>,
    ) => {
      const { treeKey, itemId } = payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      delete tree.entities[itemId];
      delete tree.loadedKeys[itemId];
      delete tree.expandedKeys[itemId];
      delete tree.checkedKeys[itemId];
      delete tree.loadingKeys[itemId];
    },
    reset: (state, { payload }: PayloadAction<string>) => {
      delete state.trees[payload];
    },

    updateFilteredNodes: (
      state,
      {
        payload,
      }: PayloadAction<{
        filter: (node: TreeElement) => boolean;
        treeKey: string;
        update: (node: TreeElement) => void;
      }>,
    ) => {
      const { treeKey, update, filter } = payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      Object.keys(tree.entities).forEach((itemId) => {
        const node = tree.entities[itemId];
        if (node && filter(node)) {
          update(node);
        }
      });
    },
    updateNodes: (
      state,
      {
        payload,
      }: PayloadAction<{
        itemIds: string[];
        treeKey: string;
        updateCallback: (node: TreeElement) => void;
      }>,
    ) => {
      const { itemIds, treeKey, updateCallback } = payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      itemIds.forEach((itemId) => {
        const item = tree?.entities[itemId];
        if (item) {
          updateCallback(item);
        }
      });
    },
    setExpandedKeys: (
      state,
      {
        payload,
      }: PayloadAction<{
        isExpanded: boolean;
        nodeIds: string[];
        treeKey: string;
      }>,
    ) => {
      const { treeKey, nodeIds, isExpanded } = payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      if (isExpanded) {
        nodeIds.forEach((nodeId) => {
          tree.expandedKeys[nodeId] = true;
        });
      } else {
        nodeIds.forEach((nodeId) => {
          delete tree.expandedKeys[nodeId];
        });
      }
    },
    setLoadedKeys: (
      state,
      { payload }: PayloadAction<{ nodeIds: string[]; treeKey: string }>,
    ) => {
      const { treeKey, nodeIds } = payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      nodeIds.forEach((nodeId) => {
        tree.loadedKeys[nodeId] = true;
      });
    },
    clearCheckedKeys: (
      state,
      action: PayloadAction<{
        treeKey: string;
      }>,
    ) => {
      const { treeKey } = action.payload;
      const tree = state.trees[treeKey];
      if (!tree) return;
      tree.checkedKeys = {};
    },
    setCheckedKeys: (
      state,
      action: PayloadAction<{
        checked: boolean;
        nodeId: string;
        treeKey: string;
      }>,
    ) => {
      const { treeKey, nodeId, checked } = action.payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      if (checked) {
        // Добавляем узел и его потомков в checkedKeys
        const nodesToCheck = getDescendantNodeIds(tree, nodeId);
        nodesToCheck.forEach((id) => {
          if (!tree.entities[id]?.isSkeleton) {
            tree.checkedKeys[id] = true;
          }
        });
      } else {
        // Удаляем узел и его потомков из checkedKeys
        const nodesToUncheck = getDescendantNodeIds(tree, nodeId);
        nodesToUncheck.forEach((id) => {
          delete tree.checkedKeys[id];
        });
      }
    },
    clearSelectedNodeId: (
      state,
      action: PayloadAction<{
        treeKey: string;
      }>,
    ) => {
      const { treeKey } = action.payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      tree.selectedNodeId = '';
    },
    setSelectedNodeId: (
      state,
      action: PayloadAction<{
        nodeId: string;
        treeKey: string;
      }>,
    ) => {
      const { treeKey, nodeId } = action.payload;
      const tree = state.trees[treeKey];
      if (!tree) return;

      tree.selectedNodeId = nodeId;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getTreeThunk.pending, (state, { meta }) => {
      const { treeKey, itemId, isRefetch, isSearch } = meta.arg;
      const tree = state.trees[treeKey];

      if (!itemId && !isSearch) {
        initializeTree(state, treeKey, getInitialTreeNodeState());
        state.trees[treeKey]!.loading = true;
      } else {
        if (!tree) return;

        const parentNode = !!itemId && tree.entities[itemId];
        if (!parentNode) return;

        tree.loadingKeys[itemId] = true;

        const childrenCount =
          parentNode.childrenIds?.length || (isRefetch ? 1 : 0);
        const totalCountOfLeafs = parentNode.totalCountOfLeafs || 0;

        const nodesToAdd: TreeElement[] = [];
        for (let i = 0; i < childrenCount; i += 1) {
          const newItemId = `${itemId}-${i}`;
          nodesToAdd.push({
            title: '',
            isSkeleton: true,
            width: Math.floor(Math.random() * 31) + 60,
            key: newItemId,
            position: i,
            disabled: true,
            checked: false,
            isLeaf: true,
            isDirectory: false,
            isCallable: false,
            itemId: newItemId,
            parent: itemId,
            childrenIds: [],
            totalCountOfLeafs:
              i === 0 ? totalCountOfLeafs + 1 - childrenCount : 1,
          });
        }

        // Добавляем placeholder узлы
        nodesToAdd.forEach((node) => {
          tree.entities[node.itemId!] = node;
        });

        parentNode.childrenIds = nodesToAdd.map((node) => node.itemId!);
        parentNode.childrenCount = parentNode.childrenIds.length;

        if (nodesToAdd.length > 0) {
          parentNode.isLeaf = false;
          tree.expandedKeys[itemId] = true;
          tree.loadedKeys[itemId] = true;
        }
      }
    });

    builder.addCase(
      getTreeThunk.rejected,
      (state, { meta, payload, error }) => {
        const { treeKey, queryParams } = meta.arg;
        const itemId = queryParams?.itemId as string | undefined;
        const tree = state.trees[treeKey];
        if (payload === 'Canceled' || !tree) return;

        tree.error = error.message || 'Ошибка загрузки дерева элементов';
        tree.loading = false;

        const parentNode = !!itemId && tree.entities[itemId];
        if (!parentNode) return;

        delete tree.loadingKeys[itemId];

        const placeholderIdsSet = new Set(
          ...(parentNode.childrenIds?.filter(
            (id) => tree.entities[id]?.isSkeleton,
          ) || []),
        );

        // Удаляем placeholder узлы
        placeholderIdsSet.forEach((id) => {
          delete tree.entities[id];
        });

        parentNode.childrenIds = parentNode.childrenIds?.filter(
          (id) => !placeholderIdsSet.has(id),
        );

        parentNode.childrenCount = parentNode?.childrenIds?.length || 0;
        parentNode.isLeaf =
          parentNode.childrenIds?.length === 0 || !parentNode.isDirectory;

        if (!parentNode.childrenIds || parentNode.childrenIds.length === 0) {
          tree.expandedKeys[itemId] = false;
          tree.loadedKeys[itemId] = false;
        }
      },
    );

    builder.addCase(getTreeThunk.fulfilled, (state, { meta, payload }) => {
      const { treeKey, itemId } = meta.arg;
      const tree = state.trees[treeKey];
      if (!tree) return;

      const treeData = Array.isArray(payload) ? payload : [];

      if (itemId) {
        delete tree.loadingKeys[itemId];
      }

      tree.loading = false;
      tree.error = undefined;

      processTreeData(treeData, tree, itemId);
    });
  },
});

export default slice.reducer;
