import { notification } from 'antd';
import { useCallback, useLayoutEffect } from 'react';
import { apiUrls, appInstanceWithoutService } from 'shared/api';
import { appErrorNotification } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useCatalogData = (
  handleClose: Callback,
): [
  typeof getStatuses,
  typeof postStatuses,
  (file: FormData) => Promise<void>,
] => {
  const [get, getStatuses] = useAxiosRequest<{
    finishedAt: string;
  }>(appInstanceWithoutService);
  const [post, postStatuses] = useAxiosRequest<unknown>(
    appInstanceWithoutService,
  );

  const getFile = async (): Promise<void> => {
    get(apiUrls.replicator.getLastFileDate).catch((err) =>
      appErrorNotification(
        'Произошла ошибка при запросе даты последней загрузки',
        err as AppError,
      ),
    );
  };

  const handleSave = useCallback(
    async (file: FormData) => {
      await post(apiUrls.replicator.postCatalog, {
        method: 'POST',
        data: file,
      })
        .then(() => {
          handleClose();
          notification.success({ message: 'Справочник успешно обновлен' });
        })
        .catch((err) =>
          appErrorNotification(
            'Произошла ошибка при загрузке справочника САДД',
            err as AppError,
          ),
        );
    },
    [handleClose, post],
  );

  useLayoutEffect(() => {
    getFile();
  }, []); //eslint-disable-line

  return [getStatuses, postStatuses, handleSave];
};
