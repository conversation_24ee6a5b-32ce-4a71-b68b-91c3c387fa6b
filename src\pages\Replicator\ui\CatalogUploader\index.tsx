import { DeleteOutlined, FileExcelOutlined } from '@ant-design/icons';
import { Tooltip, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { FC, useState } from 'react';
import { ReplicatorStore } from 'pages/Replicator';
import { AppPopup, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

interface CatalogUploaderProps extends CatalogUploaderInnerProps {
  isOpened: boolean;
}

interface CatalogUploaderInnerProps {
  onClose: Callback;
}

const CatalogUploaderInner: FC<CatalogUploaderInnerProps> = ({ onClose }) => {
  const [getFile, postFile, handleSave] =
    ReplicatorStore.hooks.useCatalogData(onClose);
  const [fileUpload, setFile] = useState<RcFile | null>(null);
  return (
    <div className={styles.container}>
      <div className={styles.history}>
        <Typography.Text>Последняя успешная загрузка:</Typography.Text>
        <Typography.Text code>
          {getFile.isPending
            ? 'Загрузка...'
            : getFile.data?.finishedAt || 'Нет данных'}
        </Typography.Text>
      </div>

      <div className={styles.uploader}>
        <Upload
          showUploadList={false}
          accept=".xls,.xlsx"
          className={styles.upload}
          type="drag"
          beforeUpload={(file) => {
            setFile(file);
            return false;
          }}
        >
          {fileUpload ? (
            <div className={styles.uploadContent}>
              <FileExcelOutlined />
              <Tooltip title={fileUpload?.name || ''}>
                <Typography.Text className={styles.uploadContentText}>
                  {fileUpload?.name || ''}
                </Typography.Text>
              </Tooltip>

              <ButtonsContainer
                buttons={[
                  {
                    title: '',
                    size: 'small',
                    danger: true,
                    ghost: true,
                    key: 'delete',
                    icon: <DeleteOutlined />,
                    onClick: (e) => {
                      e.stopPropagation();
                      setFile(null);
                    },
                  },
                ]}
              />
            </div>
          ) : (
            <Typography.Text>Выберите файл для загрузки</Typography.Text>
          )}
        </Upload>
      </div>

      <ButtonsContainer
        buttons={
          [
            {
              title: 'Загрузить',
              key: 'upload',
              type: 'primary',
              disabled: fileUpload === null,
              loading: postFile.isPending,
              onClick: async () => {
                if (fileUpload) {
                  const formData = new FormData();
                  formData.append('file', fileUpload);
                  await handleSave(formData);
                }
              },
            },
            {
              title: 'Отмена',
              loading: postFile.isPending,
              key: 'cancel',
              danger: true,
              ghost: true,
              onClick: onClose,
            },
          ] as AdditionalButton[]
        }
      />
    </div>
  );
};

export const CatalogUploader: FC<CatalogUploaderProps> = ({
  isOpened,
  onClose,
}) => (
  <AppPopup
    isOpened={isOpened}
    onClose={onClose}
    className={styles.popup}
    title="Загрузка справочника САДД"
  >
    <CatalogUploaderInner onClose={onClose} />
  </AppPopup>
);
