import axios from 'axios';
import { useCallback, useLayoutEffect } from 'react';
import { NWGConfig, SortObject } from 'widgets/NewWorkGroupTabs';
import { ColumnFilters } from 'features/CustomColumnFilters';
import {
  TableRowData,
  NestedTabsWithLazyTable,
  NestedTabsWithTable,
} from 'features/DataGrid';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useNestedData = (
  endpoint: Endpoint,
  cabinetId: string,
  handleTotal: (total: number) => void,
  handlePage: (page: number) => void,
  handleTable: React.Dispatch<React.SetStateAction<TableColumnsAndRows>>,
  sortOrder: SortObject,
  filters: ColumnFilters[],
): [
  typeof nestedTableStatuses,
  (page?: number) => void,
  isLazyTable: boolean,
  getLazyTabContent:
    | ((record: TableRowData, tabKey: string, page?: number) => Promise<void>)
    | undefined,
  loadNestedTabs?: (record: TableRowData) => Promise<void>,
] => {
  const [getNestedTable, nestedTableStatuses] =
    useAxiosRequest<TableColumnsAndRowsWithPagination>();
  const controller = new AbortController();
  const controllerLazy = new AbortController();

  const [getLazyTabContentRequest] =
    useAxiosRequest<TableColumnsAndRowsWithPagination>();

  // Для старых ленивых эндпойнтов - загрузка всех табов при раскрытии строки
  const [getNestedTabsRequest] = useAxiosRequest<NestedTabsWithTable>();

  // ПОЛНОСТЬЮ ЛЕНИВЫЕ ЭНДПОЙНТЫ: каждый таб загружается отдельно при клике
  // - nestedTable приходит с сервера с tabs, но tableData: null
  // - используется getLazyTabContent для загрузки каждого таба отдельно
  const fullyLazyEndpoints = {
    krg3_input_package_def: true,
  };

  // СТАРЫЕ ЛЕНИВЫЕ ЭНДПОЙНТЫ: все табы загружаются сразу при раскрытии строки
  // - строки приходят только с hasNested: true флагом, без nestedTable
  // - при раскрытии строки делается отдельный запрос через nestedTableLoader
  // - все табы загружаются сразу в одном запросе
  const legacyLazyEndpoints = {
    krg3_request_notice: true,
  };

  // Проверяем, является ли эндпойнт полностью ленивым
  const isFullyLazyTable = endpoint in fullyLazyEndpoints;

  // Проверяем, является ли эндпойнт старым ленивым
  const isLegacyLazyTable = endpoint in legacyLazyEndpoints;

  const getTable = useCallback(
    async (page?: number): Promise<void> => {
      try {
        const res = await getNestedTable(
          generateUrlWithQueryParams(endpoint, {
            cabinetId,
            pageSize: 10,
            pageNumber: page || 1,
            ...(sortOrder && sortOrder),
          }),
          NWGConfig.constants.NESTED_TABLE_FILTERS_ENDPOINTS.includes(endpoint)
            ? { method: 'POST', data: filters, signal: controller.signal }
            : { signal: controller.signal },
        );

        if (page) {
          handlePage(page);
        } else {
          handlePage(1);
        }
        handleTable({
          columns: res.columns,
          rows: res.rows.map((row) => {
            // Для старых ленивых эндпойнтов: добавляем пустой nestedTable для строк с hasNested
            if (isLegacyLazyTable && row.rowId?.hasNested) {
              return {
                ...row,
                nestedTable: { tabs: [] }, // Пустые табы, данные загрузятся через nestedTableLoader
              };
            }
            // Для полностью ленивых и обычных эндпойнтов оставляем данные как есть
            // (полностью ленивые уже приходят с tableData: null)
            return row;
          }),
        });
        handleTotal(res.pagination.total);
      } catch (err) {
        if (axios.isAxiosError(err)) {
          appErrorNotification(
            NWGConfig.requestErrors.nestedContent,
            err as AppError,
          );
        }
      }
    },
    [
      cabinetId,
      controller.signal,
      endpoint,
      filters,
      getNestedTable,
      handlePage,
      handleTable,
      handleTotal,
      isLegacyLazyTable,
      sortOrder,
    ],
  );

  const getLazyTabContent = useCallback(
    async (record: TableRowData, tabKey: string, page = 1): Promise<void> => {
      const itemId = record.rowId?.id;
      const nestedTable = record.nestedTable as NestedTabsWithLazyTable;

      if (!nestedTable || !nestedTable.tabs || !itemId) return;

      const tabIndex = nestedTable.tabs.findIndex(
        (tab) => String(tab.key) === tabKey,
      );
      if (tabIndex === -1) return;

      const tab = nestedTable.tabs[tabIndex];

      try {
        handleTable((lastTable) => ({
          columns: lastTable.columns,
          rows: lastTable.rows.map((row) => {
            if (row.rowId?.id === itemId) {
              const updatedNestedTable = { ...nestedTable };
              updatedNestedTable.tabs = [...nestedTable.tabs];
              updatedNestedTable.tabs[tabIndex] = {
                ...tab,
                tabStatus: {
                  error: null,
                  isError: false,
                  isLoaded: false,
                  isLoading: true,
                },
              };
              return {
                ...row,
                nestedTable: updatedNestedTable,
              };
            }
            return row;
          }),
        }));

        const queryParams: Record<string, string | number> = {
          pageSize: 10,
          pageNumber: page,
        };

        if (endpoint === 'krg3_input_package_def') {
          queryParams.packageId = itemId;
        }
        // TODO: Добавить поддержку для krg3_request_notice когда он будет переведен на ленивый режим
        // else if (endpoint === 'krg3_request_notice') {
        //   queryParams.requestId = itemId;
        // }

        const res = await getLazyTabContentRequest(
          generateUrlWithQueryParams(tab.endpoint, queryParams),
          { method: 'GET', signal: controllerLazy.signal },
        );

        handleTable((lastTable) => ({
          columns: lastTable.columns,
          rows: lastTable.rows.map((row) => {
            if (row.rowId?.id === itemId) {
              const updatedNestedTable = { ...nestedTable };
              updatedNestedTable.tabs = [...nestedTable.tabs];
              updatedNestedTable.tabs[tabIndex] = {
                ...tab,
                tableData: res,
                pagination: {
                  currentPage: page,
                  pageSize: res?.pagination?.pageSize || 10,
                  total: res?.pagination?.total || res.rows.length,
                },
                tabStatus: {
                  error: null,
                  isError: false,
                  isLoaded: true,
                  isLoading: false,
                },
              };
              return {
                ...row,
                nestedTable: updatedNestedTable,
              };
            }
            return row;
          }),
        }));
      } catch (err) {
        if (axios.isAxiosError(err)) {
          handleTable((lastTable) => ({
            columns: lastTable.columns,
            rows: lastTable.rows.map((row) => {
              if (row.rowId?.id === itemId) {
                const updatedNestedTable = { ...nestedTable };
                updatedNestedTable.tabs = [...nestedTable.tabs];
                updatedNestedTable.tabs[tabIndex] = {
                  ...tab,
                  tabStatus: {
                    error: err,
                    isError: true,
                    isLoaded: false,
                    isLoading: false,
                  },
                };
                return {
                  ...row,
                  nestedTable: updatedNestedTable,
                };
              }
              return row;
            }),
          }));
          appErrorNotification(
            NWGConfig.requestErrors.nestedContent,
            err as AppError,
          );
        }
      }
    },
    [controllerLazy.signal, endpoint, getLazyTabContentRequest, handleTable],
  );

  // Функция для загрузки всех табов при раскрытии строки (старый ленивый режим для заявок)
  const loadNestedTabs = useCallback(
    async (record: TableRowData): Promise<void> => {
      if (!isLegacyLazyTable || !record.rowId?.hasNested || !record.rowId?.id) {
        return;
      }

      const itemId = record.rowId.id;

      try {
        // Устанавливаем состояние загрузки
        handleTable((lastTable) => ({
          columns: lastTable.columns,
          rows: lastTable.rows.map((row) => {
            if (row.rowId?.id === itemId) {
              return {
                ...row,
                nestedTableStatus: {
                  error: null,
                  isError: false,
                  isLoaded: false,
                  isLoading: true,
                },
              };
            }
            return row;
          }),
        }));

        const queryParams: Record<string, string | number> = {
          cabinetId,
        };

        if (endpoint === 'krg3_request_notice') {
          queryParams.requestNoticeId = itemId;
        }

        const res = await getNestedTabsRequest(
          generateUrlWithQueryParams(`${endpoint}/tabs`, queryParams),
          { method: 'POST', signal: controller.signal },
        );

        handleTable((lastTable) => ({
          columns: lastTable.columns,
          rows: lastTable.rows.map((row) => {
            if (row.rowId?.id === itemId) {
              return {
                ...row,
                nestedTable: res,
                nestedTableStatus: {
                  error: null,
                  isError: false,
                  isLoaded: true,
                  isLoading: false,
                },
              };
            }
            return row;
          }),
        }));
      } catch (err) {
        if (axios.isAxiosError(err)) {
          handleTable((lastTable) => ({
            columns: lastTable.columns,
            rows: lastTable.rows.map((row) => {
              if (row.rowId?.id === itemId) {
                return {
                  ...row,
                  nestedTableStatus: {
                    error: err,
                    isError: true,
                    isLoaded: false,
                    isLoading: false,
                  },
                };
              }
              return row;
            }),
          }));
          appErrorNotification(
            NWGConfig.requestErrors.nestedContent,
            err as AppError,
          );
        }
      }
    },
    [
      isLegacyLazyTable,
      cabinetId,
      controller.signal,
      endpoint,
      getNestedTabsRequest,
      handleTable,
    ],
  );

  const refetch = useCallback(
    (page?: number) => {
      getTable(page);
    },
    [getTable],
  );

  useLayoutEffect(() => {
    if (endpoint !== '') {
      getTable();
    }

    return () => {
      controller.abort();
      controllerLazy.abort();
    };
  }, [endpoint, sortOrder, filters]); // eslint-disable-line

  return [
    nestedTableStatuses,
    refetch,
    isFullyLazyTable || isLegacyLazyTable, // Любой тип ленивой загрузки
    isFullyLazyTable ? getLazyTabContent : undefined, // Только для полностью ленивых
    isLegacyLazyTable ? loadNestedTabs : undefined, // Только для старых ленивых
  ];
};
