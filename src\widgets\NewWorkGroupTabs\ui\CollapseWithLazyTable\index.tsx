import { Collapse } from 'antd';
import { ReactNode, useState, FC, useEffect } from 'react';
import {
  CollapseWithLazyTableProps,
  NWGLib,
  CustomRowRender,
  HandleRow,
  TogglePopup,
  Permissions,
  NWGConfig,
} from 'widgets/NewWorkGroupTabs';
import {
  DataGrid,
  TableRowData,
  TabsWithLazyTable,
  NestedTabsWithLazyTable,
} from 'features/DataGrid';

import { appErrorNotification } from 'shared/lib';
import { ApiContainer } from 'shared/ui';

interface CollapseWithLazyTableComponentProps {
  activeEndpoint: Endpoint;
  customRowRender: CustomRowRender;
  getLazyTabContent: (
    record: TableRowData,
    tabKey: string,
    page?: number,
  ) => Promise<void>;
  handleRow: HandleRow;
  nestedContent: NestedTabsWithLazyTable;
  parentRow: TableRowData;
  permissions: Permissions;
  refetch: Callback;
  togglePopup: TogglePopup;
  isRequest?: boolean;
}

const CollapseWithLazyTableComponent: FC<
  CollapseWithLazyTableComponentProps
> = ({
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
}) => {
  const { renderCollapseTitle, customColumnsWidth } = NWGLib;
  const [activeTabKeys, setActiveTabKeys] = useState<string[]>([]);

  // Перезагружаем данные для открытых табов при обновлении основной таблицы
  useEffect(() => {
    if (activeTabKeys.length > 0) {
      const reloadPromises = activeTabKeys.map(async (tabKey) => {
        const tab = nestedContent.tabs.find((t) => String(t.key) === tabKey);
        if (
          tab &&
          !tab.tableData &&
          !tab.tabStatus?.isLoading &&
          !tab.tabStatus?.isError
        ) {
          return getLazyTabContent(parentRow, tabKey);
        }
        return Promise.resolve();
      });

      // Не ждем завершения промисов, чтобы не блокировать рендер
      Promise.all(reloadPromises).catch((error) => {
        appErrorNotification(
          NWGConfig.requestErrors.nestedContent,
          error as AppError,
        );
      });
    }
  }, [nestedContent, activeTabKeys, getLazyTabContent, parentRow]);

  const handleTabToggle = async (key: string | string[]): Promise<void> => {
    const keys = Array.isArray(key) ? key : [key];
    setActiveTabKeys(keys);

    // Загружаем содержимое для новых открытых табов
    const loadPromises = keys
      .filter((tabKey) => !activeTabKeys.includes(tabKey))
      .map(async (tabKey) => {
        const tab = nestedContent.tabs.find((t) => String(t.key) === tabKey);
        if (
          tab &&
          !tab.tableData &&
          !tab.tabStatus?.isLoading &&
          !tab.tabStatus?.isError
        ) {
          return getLazyTabContent(parentRow, tabKey);
        }
        return Promise.resolve();
      });

    await Promise.all(loadPromises);
  };

  const handlePageChange = async (
    tab: TabsWithLazyTable,
    page: number,
  ): Promise<void> => {
    await getLazyTabContent(parentRow, String(tab.key), page);
  };

  return (
    <Collapse activeKey={activeTabKeys} onChange={handleTabToggle}>
      {nestedContent.tabs.map((tab) => (
        <Collapse.Panel
          key={tab.key}
          header={renderCollapseTitle(
            tab.label,
            parentRow.rowId?.additional || '',
          )}
        >
          <ApiContainer
            error={tab.tabStatus?.error as AppError}
            isPending={false}
          >
            {tab.tableData && (
              <DataGrid
                resizableProps={{ isActive: true }}
                hideColumnSearch
                hideSorter
                columns={
                  Array.isArray(tab.tableData.columns)
                    ? tab.tableData.columns.map((column) => ({
                        ...column,
                        ...(column.columnType !== 'String' && {
                          fixed: 'right',
                          width: customColumnsWidth(column?.columnType || ''),
                          align: 'center',
                          hideSorter: true,
                          hideColumnSearch: true,
                        }),
                        render: (text: string, row: TableRowData) =>
                          customRowRender(
                            text,
                            row,
                            column,
                            refetch,
                            togglePopup,
                            handleRow,
                            activeEndpoint,
                            permissions,
                            isRequest,
                          ),
                      }))
                    : []
                }
                rows={
                  Array.isArray(tab.tableData.rows) ? tab.tableData.rows : []
                }
                tableAdditionProps={{
                  loading: !!tab.tabStatus?.isLoading,
                  size: 'small',
                  scroll: { x: '100%', y: '100%' },
                }}
                paginationProps={{
                  disabled: !!tab.tabStatus?.isLoading,
                  show: true,
                  currentPage: tab.pagination?.currentPage || 1,
                  total: tab.pagination?.total || 0,
                  pageSize: tab.pagination?.pageSize || 10,
                  showSizeChanger: false,
                  hideOnSinglePage: true,
                  onPaginatorClick: (page) => handlePageChange(tab, page),
                }}
              />
            )}
          </ApiContainer>
        </Collapse.Panel>
      ))}
    </Collapse>
  );
};

export const collapseWithLazyTable: CollapseWithLazyTableProps = (
  nestedContent,
  customRowRender,
  parentRow,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
): ReactNode => (
  <CollapseWithLazyTableComponent
    activeEndpoint={activeEndpoint}
    customRowRender={customRowRender}
    handleRow={handleRow}
    nestedContent={nestedContent}
    parentRow={parentRow}
    permissions={permissions}
    refetch={refetch}
    togglePopup={togglePopup}
    getLazyTabContent={getLazyTabContent}
    isRequest={isRequest}
  />
);
