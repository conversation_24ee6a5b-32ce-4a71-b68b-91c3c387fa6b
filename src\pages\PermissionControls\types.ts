import { permissionModalsConfig } from 'widgets/PermissionModals';
import { TableRowData } from 'features/DataGrid';

export interface PermissionInitialState {
  /** Поопапы уведомлений */
  alertPopups: {
    /** Кол-во удаленных, но активных УЗ */
    deletedUserRows: TableRowData[];
    /** Уз с истекшим сроком */
    expiredUserRows: TableRowData[];
    /** Кол-во заблокированных, но активных УЗ */
    lockedUserRows: TableRowData[];
    /** Кол-во новых пользователей из ABAC */
    newUserRows: TableRowData[];
  };
  /** Табы */
  tabs: {
    /* Юзеры, Группы, Роли */
    [permissionModalsConfig.enums.PermissionTabNames
      .Users]: TableColumnsAndRows & ApiDefaultKeys & { isLoaded: boolean };
    [permissionModalsConfig.enums.PermissionTabNames
      .Groups]: TableColumnsAndRows & ApiDefaultKeys & { isLoaded: boolean };
    [permissionModalsConfig.enums.PermissionTabNames
      .Roles]: TableColumnsAndRows & ApiDefaultKeys & { isLoaded: boolean };
    /* Отчеты */
    [permissionModalsConfig.enums.PermissionTabNames
      .Report]: TableColumnsAndRows &
      ApiDefaultKeys & {
        totalPages: number;
      };
  };
}
