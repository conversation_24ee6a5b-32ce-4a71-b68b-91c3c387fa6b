export interface ReplicatorDate {
  /** Массив объектов, отрисовывается в конкретном дне */
  data: ReplicatorData[];
  /** День календаря */
  day: string;
  /** Ключ */
  key: Key;
}

export interface ReplicatorProps {
  title: string;
}

export interface ReplicatorData {
  /** Информация для отображения в календаре */
  content: string;
  /** Таблица лога, передается в попап */
  data: TableColumnsAndRows;
  /** Ключ */
  key: Key;
  /**
   * Хедер, передается в попап, пока 2 интерфейса, как избавимся от моков будет
   * 1
   */
  logInfo: LogInfoObject | LogInfoArray[];
  /** Иконка(бадж) отображается рядом с инфой в календаре */
  type: 'warning' | 'default' | 'success' | 'error' | 'processing';
}

export interface LogInfoObject {
  /** Объем данных */
  dataVolume: string;
  /** Дата начала загрузки */
  endDate: string;
  /** Деректория загрузки */
  loadDirectory: string;
  /** Время загрузки */
  loadTime: string;
  /** Тип загрузки */
  loadType: string;
  /** Время начала загрузки */
  startDate: string;
  /** Пользователь */
  user: string;
}

/**
 * Массив с шапкой хедера в попапе лога репликатора, в титле передается название
 * ключа
 */
export interface LogInfoArray extends TitleAndKey {
  /** Описание ключа */
  description: string;
}

export interface ProgressBar {
  /** Число для расчета процентов прогресс бара */
  current: number;
  /** Состояние загрузки для дезейбла кнопок в попапе настроек */
  isLoadingDatabase: boolean;
  /** Текст загрузки */
  text: string;
  /** Число для расчета процентов прогресс бара(пакетов всего) */
  total: number;
}

export interface ProgressBarProps {
  progressBarData: ProgressBar;
}
