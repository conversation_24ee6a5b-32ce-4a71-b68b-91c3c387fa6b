import { notification } from 'antd';
import { useCallback, useEffect, useState } from 'react';
// TODO: убрать после переноса датагрида в ui либу

import type {
  TableColumnData,
  TableConfig,
  TableRowData,
  TableSizes,
  SortValue,
} from 'features/DataGrid';
import {
  OnCheck,
  OnDelete,
  OnDownload,
  OnPositionChange,
  OnSave,
  OnSort,
} from 'features/TableConfigModal/types';
import { PresetTypes } from 'shared/config/enums';
import { createConfirmModal } from 'shared/model';
import { usePreset } from 'shared/model/hooks';
import { text, constants } from '../../config';
import { parseRowsToColumns } from '../../libs';

export const useConfigActions = (
  endpoint: Endpoint,
  columns: TableColumnData[],
  tableSizes: TableSizes,
  currentSelectedProfile: TableConfig | null,
): {
  get: typeof get;
  getAll: typeof getAll;
  onCheck: OnCheck;
  onDelete: OnDelete;
  onDownload: OnDownload;
  onPositionChange: OnPositionChange;
  onReset: Callback;
  onSave: OnSave;
  onSort: OnSort;
  presetCodeStatuses: typeof presetCodeStatuses;
  rows: TableRowData[];
  selectedProfile: string;
  sortValue: SortValue;
  statuses: typeof statuses;
} => {
  const [selectedProfile, setSelectedProfile] = useState('');
  const [sortValue, setSortValue] = useState<SortValue>(null);

  /* Колонки таблиц как ряды для настройки */
  const [rows, setRows] = useState<TableRowData[]>([]);

  const { presetCodeStatuses, statuses, createUpdate, getAll, remove, get } =
    usePreset<TableConfig>(endpoint, PresetTypes.TABLE_CONFIG);

  /** Парсинг колонок в ряды для настройки */
  const reset = (): void => {
    setSortValue(null);
    setRows(() =>
      columns.map((column, index) => ({
        show: true,
        tabTitle: '',
        name: column.title,
        width: tableSizes[column.title as string] ?? column.width ?? 100,
        position: index + 1,
        key: column.key as Key,
        sort: null,
        sortable: Boolean(column.sortable),
        disabled: constants.DISABLED_COLUMN_TYPES.includes(
          column?.columnType || '',
        ),
        columnUuid: column.columnUuid,
      })),
    );
  };

  /** Парсинг рядов в колонки */
  const parseRows = (
    rowsForParse: TableRowData[],
    sortValueForUpdate: SortValue,
  ): void => {
    setRows(
      rowsForParse.map((column) => ({
        ...JSON.parse(JSON.stringify(column)),
        width: tableSizes[column.name as string] ?? column.width ?? 100,
        sort:
          sortValueForUpdate &&
          column.columnUuid === sortValueForUpdate.columnUuid
            ? sortValueForUpdate.value
            : null,
      })),
    );
  };

  /** Рендер текущего состояния из дефолтного профиля или парсинг колонок дефолт */
  useEffect(() => {
    if (currentSelectedProfile && currentSelectedProfile.asRows?.length > 0) {
      parseRows(
        currentSelectedProfile.asRows,
        currentSelectedProfile.sortValue,
      );
      setSortValue(currentSelectedProfile.sortValue);
    } else {
      reset();
    }
  }, [columns, currentSelectedProfile, tableSizes]);

  return {
    selectedProfile,
    presetCodeStatuses,
    statuses,
    getAll,
    rows,
    get,
    sortValue,

    /** Обработчик сброса */
    onReset: useCallback<Callback>(() => {
      createConfirmModal({
        isConfirmDanger: true,
        title: text.reset,
        message: text.resetConfirmMessage,
        onConfirm: () => {
          reset();
        },
      });
    }, []),

    /** Обработчик удаления */
    onDelete: useCallback<OnDelete>((currentSelectValue) => {
      createConfirmModal({
        isConfirmDanger: true,
        title: text.deleteTitle,
        message: text.deleteMessage(currentSelectValue),
        onConfirm: () => {
          remove(currentSelectValue);
          setSelectedProfile('');
        },
      });
    }, []),

    /** Обработчик загрузки пресета */
    onDownload: useCallback<OnDownload>((currentSelectValue) => {
      createConfirmModal({
        title: text.downloadTitle,
        message: text.downloadConfirmMessage(currentSelectValue),
        onConfirm: async () => {
          setSelectedProfile(currentSelectValue);
          const data = await get(currentSelectValue);
          parseRows(data.asRows, data.sortValue);
        },
      });
    }, []),

    /** Обработчик сохранения */
    onSave: useCallback<OnSave>(
      (inputNameValue, isDefaultProfile) =>
        new Promise<void>((resolve) => {
          createConfirmModal({
            onConfirm: async () => {
              const profile = {
                asRows: rows,
                asColumns: parseRowsToColumns(rows, columns),
                sortValue,
              };
              await createUpdate(inputNameValue, profile);

              notification.success({
                message: text.onSaveSuccessTitle,
                description: text.onSaveSuccessMessage(inputNameValue),
              });

              resolve();
            },
            title: text.onSaveTitle,
            message: (() => {
              const { data: presets } = presetCodeStatuses;
              if (presets === null) return '';

              const isReSave = presets.includes(inputNameValue);

              if (isDefaultProfile) {
                return isReSave
                  ? text.onDefaultProfileReSave
                  : text.onDefaultProfileSave;
              }

              return isReSave
                ? text.onDuplicateSaveMessage(inputNameValue)
                : text.onSaveMessage(inputNameValue);
            })(),
          });
        }),
      [presetCodeStatuses, rows, columns],
    ),

    /** Обработчик чекбокса */
    onCheck: useCallback<OnCheck>(
      (isChecked, row) =>
        setRows((prev) =>
          prev.map((item) => {
            if (item.key === row.key) {
              return {
                ...JSON.parse(JSON.stringify(item)),
                show: isChecked,
                position: isChecked
                  ? prev.filter(({ show }) => show).length + 1
                  : prev.length,
              };
            }

            if (
              !isChecked &&
              (item.position as number) > (row.position as number)
            ) {
              return {
                ...JSON.parse(JSON.stringify(item)),
                position: (item.position as number) - 1,
              };
            }

            return item;
          }),
        ),
      [],
    ),

    /** Обработчик изменения позиции */
    onPositionChange: useCallback((newPosition, row) => {
      setRows((prev) => {
        const visibleRows = prev.filter(({ show }) => show);
        const maxPosition = visibleRows.length;

        // Проверяем валидность новой позиции
        const validPosition = Math.min(newPosition, maxPosition);

        // Проверяем все условия для изменения позиции
        const isValidMove =
          validPosition > 0 &&
          !row.disabled &&
          !prev[validPosition - 1].disabled;

        if (!isValidMove) {
          return prev;
        }

        // Если все проверки пройдены, обновляем позиции
        return prev.map((item) => {
          if (item.key === row.key) {
            return {
              ...JSON.parse(JSON.stringify(item)),
              position: validPosition,
            };
          }
          if (item.position === validPosition) {
            return {
              ...JSON.parse(JSON.stringify(item)),
              position: row.position,
            };
          }
          return item;
        });
      });
    }, []),

    onSort: useCallback<OnSort>((columnUuid, newSortValue) => {
      setSortValue(
        newSortValue === null ? null : { columnUuid, value: newSortValue },
      );
      setRows((prev) =>
        prev.map((row) => {
          if (row.columnUuid === columnUuid) {
            return { ...row, sort: newSortValue };
          }

          return { ...row, sort: null };
        }),
      );
    }, []),
  };
};
