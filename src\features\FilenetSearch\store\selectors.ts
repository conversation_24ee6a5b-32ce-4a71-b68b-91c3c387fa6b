import { createSelector } from '@reduxjs/toolkit';
import { selectSelf } from 'shared/lib';
import { slice } from './reducers';

const filenetSearchSelfSelector = selectSelf(slice.name);

export const extendedSearchSelector = createSelector(
  filenetSearchSelfSelector,
  (state) => state.extendedSearch,
);

export const mainSearchSelector = createSelector(
  filenetSearchSelfSelector,
  (state) => state.mainSearch,
);

export const isExtendedSearchOpenSelector = createSelector(
  filenetSearchSelfSelector,
  (state) => state.isExtendedSearchOpen,
);

export const isExtendedSearchEmptySelector = createSelector(
  extendedSearchSelector,
  (extendedSearch) =>
    Object.entries(extendedSearch)
      .filter(([key]) => key !== 'documentContentSearchTypes')
      .every(
        ([_, value]) =>
          value === '' ||
          value === null ||
          value === undefined ||
          (Array.isArray(value) && value.length === 0),
      ),
);
