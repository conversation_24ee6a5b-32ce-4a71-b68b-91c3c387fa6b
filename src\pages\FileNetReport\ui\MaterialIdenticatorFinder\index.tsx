import {
  DeleteTwoTone,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Button, InputNumber, Space, Typography } from 'antd';

import { FC, useState } from 'react';
import { Link } from 'react-router-dom';
import { DataGrid } from 'features/DataGrid';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';

import { selectors, reducers, actions } from '../../store';

import styles from './styles.module.scss';

const ID_IS_ALREADY_HAVE = 'Материал с таким ID уже добавлен';
const BASIC_ERROR =
  'Материал с таким ID не существует или у Вас нет прав доступа нему';

export const MaterialIdenticatorFinder: FC = () => {
  const { isPending, ids, items } = useAppSelector(
    selectors.materialIdenticatorSelector,
  );
  const { deleteOneIdenticator } = useCreateSliceActions(
    reducers.slice.actions,
  );

  const [materialIdInput, setMaterialIdInput] = useState<number | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  const dispatch = useAppDispatch();

  const isDisabled =
    materialIdInput === null || isPending || errorMessage !== '';

  const onSubmit = (): void => {
    if (materialIdInput && !isDisabled) {
      dispatch(actions.getMaterialIdsThunk(materialIdInput))
        .unwrap()
        .then(() => {
          setMaterialIdInput(null);
        })
        .catch(() => {
          setErrorMessage(BASIC_ERROR);
        });
    }
  };

  return (
    <DataGrid
      columns={[
        {
          title: 'ID',
          dataIndex: 'id',
          key: 'ID',
          width: 100,
          hideSorter: true,
          hideColumnSearch: true,
          render: (id, row) => (
            <Link
              to={generateUrlWithQueryParams('/file-net', {
                popupId: id,
                tableType: row.type === 'AUDIT' ? 'dossier' : 'cards',
              })}
              target="_blank"
            >
              ID{id}
            </Link>
          ),
        },
        {
          title: 'Путь и имя файла',
          dataIndex: 'title',
          key: 'title',
        },
        {
          title: '',
          dataIndex: 'id',
          key: 'delete',
          width: 80,
          align: 'center',
          hideSorter: true,
          hideColumnSearch: true,
          render: (_, row) => (
            <DeleteTwoTone
              twoToneColor="#FF4D4F"
              onClick={() => deleteOneIdenticator(row.id)}
              title="Удалить"
            />
          ),
        },
      ]}
      rows={items}
      tableAdditionProps={{
        size: 'small',
        scroll: { x: '100%', y: 200 },
        bordered: true,
      }}
      additionalComponent={
        <div className={styles.row}>
          <div className={styles.width100}>
            <InputNumber
              maxLength={4000}
              className={styles.width100}
              value={materialIdInput}
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  onSubmit();
                }
              }}
              onChange={(value) => {
                setMaterialIdInput(value === 0 ? null : value);

                if (value && ids.includes(value)) {
                  setErrorMessage(ID_IS_ALREADY_HAVE);
                } else {
                  setErrorMessage('');
                }
              }}
              disabled={isPending}
              placeholder="Идентификатор материала"
              min={0}
              controls={false}
              status={errorMessage ? 'error' : ''}
              addonAfter={
                isPending && (
                  <Space>
                    <LoadingOutlined /> Загрузка...
                  </Space>
                )
              }
            />
            {errorMessage && (
              <Typography.Text type="danger" className={styles.error}>
                {errorMessage}
              </Typography.Text>
            )}
          </div>

          <Button
            icon={<PlusOutlined />}
            type="primary"
            disabled={isDisabled}
            onClick={onSubmit}
          >
            Добавить
          </Button>
        </div>
      }
    />
  );
};
