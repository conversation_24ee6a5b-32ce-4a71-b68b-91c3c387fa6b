import { useCallback, useEffect, useRef } from 'react';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { useAppDispatch, useCreateSliceActions } from 'shared/model';

import { getFilesAndDossier } from '../actions';
import { slice } from '../reducer';

type HandleGetFiles = (isPagination?: boolean) => void;
type ToggleCardsAndDossier = (isAuditCards: boolean) => void;
type OnPagination = (page: number) => void;

export const useFilenetApi = (): {
  getFiles: HandleGetFiles;
  onPagination: OnPagination;
  toggleCardsAndDossier: ToggleCardsAndDossier;
} => {
  const { setPage } = useCreateSliceActions(slice.actions);

  const dispatch = useAppDispatch();
  const promiseRef = useRef<{ abort: Callback } | null>(null);

  const getFiles = useCallback<HandleGetFiles>(
    (isPagination = false) => {
      if (!isPagination) {
        setPage(1);
      }
      if (promiseRef.current) {
        promiseRef.current.abort();
      }
      promiseRef.current = dispatch(getFilesAndDossier());
    },
    [dispatch, setPage],
  );

  const toggleCardsAndDossier = useCallback<ToggleCardsAndDossier>(
    (isAuditCards) => {
      setPage(1);
      dispatch(getFilesAndDossier(isAuditCards));
    },
    [dispatch, setPage],
  );

  const onPagination = useCallback<OnPagination>((page) => {
    setPage(page);
    getFiles(true);
  }, []); // eslint-disable-line

  useEffect(() => {
    /**
     * Запрос необхоимый файлнету для генерации отчетов, необходим при старте
     * старницы
     */
    filenetServiceInstance.get(apiUrls.fileNet.reportsChangelog);
  }, []); // eslint-disable-line

  return {
    toggleCardsAndDossier,
    onPagination,
    getFiles,
  };
};
