import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import type { TableRowData } from 'features/DataGrid';

import { apiUrls, filenetServiceInstance } from 'shared/api';
import { createBasicClosableNotice, ErrorWithoutShow } from 'shared/model';
import { deleteKeysFromObject } from 'shared/model/handleKeysFromObject';

import type { ElasticTableResponse } from '../types';

export const getFilesAndDossier = createAsyncThunk<
  { isAuditCards: boolean; rows: TableRowData[]; totalItems: number },
  void | boolean | undefined,
  { state: import('processes/store').RootState }
>(
  'fileNet/getFilesAndDossierThunk',
  async (isCards, { getState, rejectWithValue, signal }) => {
    const {
      apiData: { docTypeIds },
      mainTable: {
        dossier,
        cards,
        isAuditCards: isAuditCardsStore,
        gibrSelectedFilter,
        koSelectedFilter,
        territorialStructureSelectedFilter,
        organizationsSelectedFilter,
      },
    } = getState().fileNet;

    const isAuditCards = isCards !== undefined ? isCards : isAuditCardsStore;

    const { extendedSearch, mainSearch, isExtendedSearchOpen } =
      getState().filenetSearch;

    try {
      const { data } = await filenetServiceInstance.post<ElasticTableResponse>(
        apiUrls.fileNet.dossierAndCardsTable,
        {
          docTypeIds,
          unitIds: (
            [
              gibrSelectedFilter,
              koSelectedFilter,
              organizationsSelectedFilter,
              territorialStructureSelectedFilter,
            ].filter((item) => item !== null) as TableRowData[]
          ).map((item) => deleteKeysFromObject(item, ['children', 'key'])),
          ui: {
            first:
              ((isAuditCards
                ? dossier.pagination.currentPage
                : cards.pagination.currentPage) -
                1) *
              10,
            rows: 10,
            sortField: null,
            sortOrder: 1,
            filters: null,
          },
          disablePermissionControl: false,
          isAuditCards,
          ...(isExtendedSearchOpen ? { extendedSearch } : { mainSearch }),
        },
        { signal },
      );

      return {
        rows: data.model.data.map(
          ({
            title,
            id,
            publishedDatetime,
            contentPath,
            uploadDatetime,
            organizationUnitsData,
          }) => ({
            title,
            id,
            publishedDatetime,
            contentPath,
            key: id + title,
            uploadDatetime,
            ...(organizationUnitsData.length === 2
              ? {
                  KO: organizationUnitsData[0]?.name ?? '',
                  region: organizationUnitsData[1]?.name ?? '',
                }
              : organizationUnitsData.length === 3
              ? {
                  MI: organizationUnitsData[0]?.name ?? '',
                  KO: organizationUnitsData[1]?.name ?? '',
                  region: organizationUnitsData[2]?.name ?? '',
                }
              : {}),
          }),
        ),
        totalItems: data.model.totalRecords,
        isAuditCards,
      };
    } catch (err) {
      if (axios.isCancel(err)) {
        throw new ErrorWithoutShow('canceled');
      }

      if (axios.isAxiosError(err)) {
        if (err.response?.status === 417) {
          createBasicClosableNotice({
            message: 'Внимание',
            description:
              'Найдено слишком много записей (более 10000). Необходимо сузить параметры поиска',
          });

          throw new ErrorWithoutShow();
        }

        return rejectWithValue(err);
      }
      throw err;
    }
  },
  {
    serializeError(error) {
      return error instanceof ErrorWithoutShow
        ? new ErrorWithoutShow()
        : new Error();
    },
  },
);
