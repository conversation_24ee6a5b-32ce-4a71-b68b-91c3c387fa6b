.popup {
  height: min-content;
  width: 650px;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history {
  display: flex;
  align-items: center;
  gap: 10px;
}

.uploader {
  width: 100%;
}

.upload {
  background: white !important;

  :global(.ant-upload.ant-upload-drag) {
    background: white !important;
  }

  :global(.ant-upload-drag-container) {
    vertical-align: top !important;
  }

  &Content {
    display: flex;
    width: 95%;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    gap: 5px;

    &Text {
      max-width: 500px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
