/**
 * Модуль инициализации env-переменных
 *
 * @store
 * @remark Если не найдено значение хоть одной переменной,
 * Приложение сразу выбросит ошибку, при инициализации модуля
 */
/**
 * Получение env-переменной
 *
 * @throwable
 */

// Безопасный доступ к переменным окружения в браузере
const getProcessEnv = (): Record<string, string | undefined> => {
  // В браузере window.__ENV__ может быть установлен через webpack.DefinePlugin
  // или вернемся к пустому объекту, если process недоступен
  if (typeof window !== 'undefined' && window.__ENV__) {
    return window.__ENV__;
  }

  // Для Node.js среды
  if (typeof process !== 'undefined' && process.env) {
    return process.env as Record<string, string | undefined>;
  }

  // Возвращаем пустой объект, если ничего не доступно
  return {};
};

// Типизация для window.__ENV__
declare global {
  interface Window {
    __ENV__?: Record<string, string | undefined>;
  }
}

export const getEnvVar = (key: string): string | never => {
  const env = getProcessEnv();
  if (env[key] === undefined) {
    // В случае отсутствия переменной в браузере, используем значения по умолчанию
    // для критичных переменных
    if (key === 'NODE_ENV') return 'production';
    throw new Error(`Env variable ${key} is required`);
  }
  return env[key] as string;
};

/** Режим запуска программы */
export const NODE_ENV = getEnvVar('NODE_ENV');

/** Режим разработки (все, кроме production) */
export const isDevEnv = NODE_ENV !== 'production';

// Безопасное получение переменной REACT_APP_ZT
export const IS_ZT_BUILD = getProcessEnv()['REACT_APP_ZT'] === 'true';
