import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  permissionModalsStore,
  permissionModalsConfig,
} from 'widgets/PermissionModals';
import { TableRowData } from 'features/DataGrid';
import { calculateUsers } from '../lib';

import { PermissionInitialState } from '../types';

const processAndSortRows = (rows: TableRowData[]): void => {
  rows.sort((a, b) => {
    const titleA = a.tabTitle || '';
    const titleB = b.tabTitle || '';
    return titleA.localeCompare(titleB, 'ru', { numeric: true });
  });

  rows.forEach((item: TableRowData) => {
    if (
      item.checkboxStatus &&
      Object.hasOwn(item.checkboxStatus, 'isDeleted') &&
      !Object.hasOwn(item.checkboxStatus, 'isUndeleted')
    ) {
      // Добавляем статус неудаленной роли, если его нет
      item.checkboxStatus.isUndeleted = !item.checkboxStatus.isDeleted;
    }

    if (Array.isArray(item.children) && item.children.length > 0) {
      processAndSortRows(item.children);
    }
  });
};

const defaultPermissionState = {
  isPending: false,
  error: null,
  isLoaded: false,
  rows: [],
  columns: [],
};

const initialState: PermissionInitialState = {
  tabs: {
    [permissionModalsConfig.enums.PermissionTabNames.Users]:
      defaultPermissionState,
    [permissionModalsConfig.enums.PermissionTabNames.Groups]:
      defaultPermissionState,
    [permissionModalsConfig.enums.PermissionTabNames.Roles]:
      defaultPermissionState,
    [permissionModalsConfig.enums.PermissionTabNames.Report]: {
      isPending: false,
      rows: [],
      columns: [],
      error: null,
      totalPages: 0,
    },
  },
  alertPopups: {
    deletedUserRows: [],
    expiredUserRows: [],
    lockedUserRows: [],
    newUserRows: [],
  },
};

export const slice = createSlice({
  name: 'newPermissionControls',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
    clearAlertPopup: (
      state,
      { payload }: PayloadAction<keyof typeof initialState.alertPopups>,
    ) => {
      state.alertPopups[payload] = [];
    },
  },
  extraReducers: (builder) => {
    (
      [
        {
          enumValue: permissionModalsConfig.enums.PermissionTabNames.Users,
          thunk: permissionModalsStore.actions.getUsersThunk,
        },
        {
          enumValue: permissionModalsConfig.enums.PermissionTabNames.Groups,
          thunk: permissionModalsStore.actions.getGroupsThunk,
        },
        {
          enumValue: permissionModalsConfig.enums.PermissionTabNames.Roles,
          thunk: permissionModalsStore.actions.getRolesThunk,
        },
      ] as {
        enumValue:
          | permissionModalsConfig.enums.PermissionTabNames.Users
          | permissionModalsConfig.enums.PermissionTabNames.Groups
          | permissionModalsConfig.enums.PermissionTabNames.Roles;
        thunk: typeof permissionModalsStore.actions.getUsersThunk;
      }[]
    ).forEach(({ enumValue, thunk }) => {
      builder.addCase(thunk.pending, (state) => {
        state.tabs[enumValue].error = null;
        state.tabs[enumValue].isPending = true;
      });
      builder.addCase(
        thunk.fulfilled,
        (state, { payload: { columns, rows } }) => {
          if (
            enumValue === permissionModalsConfig.enums.PermissionTabNames.Users
          ) {
            const {
              deletedUserRows,
              expiredUserRows,
              lockedUserRows,
              newUserRows,
            } = calculateUsers(rows);

            state.alertPopups.deletedUserRows = deletedUserRows;
            state.alertPopups.expiredUserRows = expiredUserRows;
            state.alertPopups.lockedUserRows = lockedUserRows;
            state.alertPopups.newUserRows = newUserRows;
          }

          if (
            enumValue === permissionModalsConfig.enums.PermissionTabNames.Roles
          ) {
            processAndSortRows(rows);
          }

          state.tabs[enumValue].isPending = false;
          state.tabs[enumValue].isLoaded = true;
          state.tabs[enumValue].rows = rows;
          state.tabs[enumValue].columns = columns;
        },
      );
      builder.addCase(thunk.rejected, (state, action) => {
        state.tabs[enumValue].isPending = false;
        state.tabs[enumValue].error = action.error;
      });
    });
  },
});
