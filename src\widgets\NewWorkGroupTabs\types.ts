import { TableProps } from 'antd';
import { ReactNode } from 'react';
import { ColumnFilterActions } from 'features/CustomColumnFilters';
import type {
  NestedTabsWithTable,
  NestedTabsWithLazyTable,
  TableColumnData,
  TableRowData,
} from 'features/DataGrid';
import { PermissionsInitial } from 'entities/Permissions';
import { CabinetStatuses } from 'shared/config/enums';
import { RequestStatuses } from 'shared/model';

export interface NewWorkGroupTabsProps {
  additionalParams: Record<string, string>;
}

export interface HeaderData {
  cabinetId: string;
  defaultTab: string;
  status: CabinetStatuses;
  tabHeaders: TabHeaders[];
  title: string;
}

export type TabsItemsResponse = import('rc-tabs/lib/interface').Tab & {
  endpoint: Endpoint;
  isMarked: boolean;
};

export interface TabHeaders {
  data: TabHeadersData[];
  key: string;
  title: string;
}

export interface TabHeadersData {
  content: string;
  isEditable: boolean;
  isLink: boolean;
  key: string;
  title: string;
}

export interface MainTabsProps {
  activeKey: string;
  counter: number;
  handleChangeTab: (key: string, endpoint: Endpoint) => void;
  tabs: RequestStatuses<TabsItemsResponse[]>;
}

export interface TabsOrTableProps {
  activeEndpoint: Endpoint;
  additionalButtons: AdditionalButton[];
  clearSelection: Callback;
  columnFilters: ColumnFilterActions;
  columnSorter: ColumnSorter;
  currentPage: number;
  data: TableColumnsAndRowsWithPagination | TabsItemsResponse[];
  getData: (page?: number) => void;
  handleRowData: (value: Partial<TableRowData>) => void;
  handleSelect: (value: TableRowSelection) => void;
  handleTab: (key: string, tableEndpoint: Endpoint) => void;
  isPending: boolean;
  isShowSelects: boolean;
  nestedActiveKey: string;
  nestedData: TableColumnsAndRowsWithPagination | null;
  permissions: Permissions;
  selectedTableKeys: Key[];
  togglePopup: TogglePopup;
  total: number;
  getLazyTabContent?: (
    record: TableRowData,
    tabKey: string,
    page?: number,
  ) => Promise<void>;
  loadNestedTabs?: (record: TableRowData) => Promise<void>;
}

interface ColumnSorter {
  handleSort: (sortParams: SortObject, isNested: boolean) => void;
  sortOrder: SortObject;
}

export type SortObject = { direction: SortOrder; sort: string } | null;

export type SortOrder = 'DESC' | 'ASC';

export interface PopupsProps {
  cabinetId: string;
  isFullFilesAccess: boolean;
  permissions: Permissions;
  popup: Record<
    keyof typeof import('widgets/NewWorkGroupTabs/config').popupsState,
    boolean
  >;
  refetch: Callback;
  refetchHeader: Callback;
  rowAdditional: {
    data: Partial<TableRowData>;
    reset: Callback;
  };
  togglePopup: TogglePopup;
}

export type CustomRowRender = (
  text: string,
  row: TableRowData,
  column: TableColumnData,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  tableEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
) => JSX.Element;

export type TogglePopup = (
  name: keyof typeof import('widgets/NewWorkGroupTabs/config').popupsState,
) => void;

export type AdditionalParams = Partial<TableRowData>;

export type HandleRow = (value: AdditionalParams) => void;

export type CollapseWithTableProps = (
  nestedContent: NestedTabsWithTable,
  customRowRender: CustomRowRender,
  parentRow: TableRowData,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
) => ReactNode;

export type CollapseWithLazyTableProps = (
  nestedContent: NestedTabsWithLazyTable,
  customRowRender: CustomRowRender,
  parentRow: TableRowData,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  getLazyTabContent: (
    record: TableRowData,
    tabKey: string,
    page?: number,
  ) => Promise<void>,
  isRequest?: boolean,
) => ReactNode;

export type TableConfigProps = (
  collapseWithTable: CollapseWithTableProps,
  customRowRender: CustomRowRender,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  isRequest?: boolean,
  rows?: TableRowData[],
) => TableProps<TableRowData>;

export type TableConfigWithLazyTabsProps = (
  collapseWithLazyTable: CollapseWithLazyTableProps,
  customRowRender: CustomRowRender,
  refetch: Callback,
  togglePopup: TogglePopup,
  handleRow: HandleRow,
  activeEndpoint: Endpoint,
  permissions: Permissions,
  getLazyTabContent: (
    record: TableRowData,
    tabKey: string,
    page?: number,
  ) => Promise<void>,
  isRequest?: boolean,
  rows?: TableRowData[],
) => TableProps<TableRowData>;

export type Permissions = PermissionsInitial;
