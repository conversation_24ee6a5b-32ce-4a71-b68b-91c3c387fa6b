@import 'src/app/styles/mixins';

.calendar {
  position: relative;

  &Header {
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 98%;
    overflow-x: auto;
    @include scrollBar;

    &Buttons {
      display: flex;
      gap: 30px;
    }

    &Select {
      display: flex;
      gap: 20px;

      &Box {
        display: flex;
        gap: 5px;
        align-items: center;
        @include defaultBorder;

        padding: 3px 12px;
        border-radius: 2px;

        &Text {
          margin-bottom: 1px;
        }

        &Action {
          :global(.ant-select-selection) {
            display: flex;
            align-items: center;
            border: none !important;
          }

          :global(.ant-select-selection-selected-value) {
            width: 50px !important;
          }
        }
      }
    }
  }
}
