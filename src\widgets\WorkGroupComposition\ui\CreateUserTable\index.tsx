import { Select, Table } from 'antd';
import { FC } from 'react';
import {
  CreateUserTableProps,
  WorkGroupCompositionStore,
} from 'widgets/WorkGroupComposition';
import { DataGrid } from 'features/DataGrid';
import { useAppSelector, useCreateSliceActions } from 'shared/model';

import styles from './styles.module.scss';

export const CreateUserTable: FC<CreateUserTableProps> = ({
  isFullSize,
  showOnlyFounded = true,
  getNestedUserTable,
}) => {
  const { handleSelect, handleCheckKeys } = useCreateSliceActions(
    WorkGroupCompositionStore.reducers.slice.actions,
  );
  const composition = useAppSelector(
    WorkGroupCompositionStore.selectors.compositionSelector,
  );

  return (
    <DataGrid
      showOnlyFounded={showOnlyFounded}
      hideColumnSearch
      hideSorter
      nestedTableProps={{
        tableAdditionProps: { size: 'small' },
        hideSorter: true,
        hideColumnSearch: true,
        additionalClassNames: { table: styles.nested },
      }}
      searchProps={{
        show: true,
        label: 'Введите имя нужного пользователя для добавления в КРГ',
      }}
      additionalClassNames={{
        container: isFullSize
          ? styles.tableContainerFull
          : styles.tableContainer,
        spinner: isFullSize ? styles.tableSpinnerFull : styles.tableSpinner,
        table: isFullSize ? styles.tableFull : styles.table,
      }}
      nestedTableLoader={(record) => {
        getNestedUserTable(record?.rowId?.hfpsidUserId as string);
      }}
      columns={composition.creation.table.columns.map((column) => {
        if (column.columnType === 'Select') {
          return {
            ...column,
            render: (text, row) => (
              <Select
                placeholder="Выберите доп. полномочия"
                value={(row.permissions as string) ?? null}
                className={styles.selectItem}
                onChange={(value: string) => {
                  handleSelect({ value, index: Number(row.key) - 1 });
                }}
              >
                {composition.update.select.array.map((item) => (
                  <Select.Option value={item.value} key={item.key}>
                    {item.title}
                  </Select.Option>
                ))}
              </Select>
            ),
          };
        }
        return column;
      })}
      resizableProps={{ isActive: true }}
      rows={composition.creation.table.rows}
      columnsProps={{
        shouldCellUpdate: (prev, next) =>
          prev.permissions !== next.permissions ||
          prev.checked !== next.checked,
      }}
      tableAdditionProps={{
        size: isFullSize ? 'middle' : 'small',
        scroll: { y: isFullSize ? '60vh' : '32vh', x: '100%' },
        rowSelection: {
          onChange: handleCheckKeys,
          selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
        },
        pagination: {
          hideOnSinglePage: true,
          showSizeChanger: false,
          pageSize: 12,
          position: ['bottomCenter'],
        },
      }}
    />
  );
};
