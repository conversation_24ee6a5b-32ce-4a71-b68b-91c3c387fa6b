import { TableProps } from 'antd';
import type { TableConfigWithLazyTabsProps } from 'widgets/NewWorkGroupTabs';
import { NestedTabsWithLazyTable, TableRowData } from 'features/DataGrid';

export const tableConfigWithLazyTabs: TableConfigWithLazyTabsProps = (
  collapseWithLazyTable,
  customRowRender,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  getLazyTabContent,
  isRequest,
  rows?,
): TableProps<TableRowData> => ({
  size: 'middle',
  pagination: false,
  scroll: { x: '100%', y: '100%' },
  ...(rows &&
    rows.some((row) => Object.hasOwn(row, 'nestedTable')) && {
      expandable: {
        rowExpandable: (row) => Object.hasOwn(row, 'nestedTable'),
        expandedRowRender: (row) =>
          collapseWithLazyTable(
            row.nestedTable as NestedTabsWithLazyTable,
            customRowRender,
            row,
            refetch,
            togglePopup,
            handleRow,
            activeEndpoint,
            permissions,
            getLazyTabContent,
            isRequest,
          ),
      },
    }),
});
