import { useCallback, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LoadingStatusState, loadingStatusStore } from 'entities/LoadingStatus';

export const useGetProgressBar = (): [
  LoadingStatusState,
  () => Promise<void>,
] => {
  const dispatch = useDispatch();
  const loadingStatus = useSelector(
    loadingStatusStore.selectors.selectLoadingStatus,
  );

  const getProgress = useCallback(async () => {
    dispatch(loadingStatusStore.thunks.getLoadingStatus());
  }, [dispatch]);

  const POLLING_INTERVAL = 10000;

  useLayoutEffect(() => {
    getProgress();

    const interval = setInterval(getProgress, POLLING_INTERVAL);

    return () => clearInterval(interval);
  }, [getProgress]);

  return [loadingStatus, getProgress];
};
