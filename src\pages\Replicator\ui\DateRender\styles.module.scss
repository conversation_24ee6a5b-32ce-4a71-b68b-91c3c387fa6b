@import 'src/app/styles/mixins';

.cell {
  position: relative;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 2px;
  flex-direction: column;

  &Badges {
    position: absolute;
    top: 0;
    right: 20%;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    width: 78%;
    overflow-x: hidden;
  }

  &Info {
    display: flex;
    align-items: flex-end;
    width: 30%;
    overflow: hidden;
    @include fadeAnimation;
  }

  &Title {
    margin: 4px 0 0;

    @media screen and (max-width: 1500px) {
      font-size: 10px;
    }
  }

  &Button {
    background: none;
    border: none;
    padding: 0 2px;
    margin: 0;
    cursor: pointer;
    list-style: none;
    text-align: left;

    &:hover {
      background-color: rgb(62 146 250 / 34%);
    }
  }
}
