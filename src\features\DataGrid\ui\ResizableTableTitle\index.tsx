import classNames from 'classnames';
import type { FC, HTMLAttributes, SyntheticEvent } from 'react';
import { useState } from 'react';
import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';

import { constants } from '../../config';

import styles from './styles.module.scss';

interface ResizableTitleProps extends HTMLAttributes<unknown> {
  onResize: (e: SyntheticEvent<Element>, data: ResizeCallbackData) => void;
  width: number;
}

export const ResizableTableTitle: FC<ResizableTitleProps> = ({
  onResize,
  width,
  ...restProps
}): JSX.Element => {
  const [newWidth, setNewWidth] = useState(width);

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={newWidth}
      minConstraints={[constants.MIN_COLUMN_WIDTH, constants.MIN_COLUMN_WIDTH]}
      height={0}
      onResizeStop={(_, data) => {
        onResize(_, { ...data, size: { ...data.size, width: newWidth } });
      }}
      onResize={(_, { size }) => setNewWidth(size.width)}
      handle={
        <span
          role="separator"
          className="react-resizable-handle"
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      }
      draggableOpts={{ enableUserSelectHack: false }}
      className={classNames(styles.header)}
    >
      <th {...restProps} />
    </Resizable>
  );
};
