import moment from 'moment';
import {
  DEFAULT_DATE_TIME_VARIANT,
  DEFAULT_DATE_VARIANT,
} from 'shared/config/constants';
import { ColumnFilters, SpecialColumnHandler } from '../../types';

/**
 * Применяет набор фильтров к строке данных таблицы
 *
 * Функция поддерживает следующие типы фильтрации:
 *
 * - Фильтрация по списку значений (values)
 * - Текстовый поиск (query)
 * - Числовой диапазон (fromNum, toNum)
 * - Диапазон дат (from, to) с поддержкой различных форматов через moment.js
 *
 * @example
 *   // Пример использования с датами
 *   const row = { date: '13.02.2023 17:07:08' };
 *   const filters = [
 *     {
 *       column: 'date',
 *       from: '01.02.2023',
 *       to: '28.02.2023',
 *       query: '',
 *       values: [],
 *       fromNum: '',
 *       toNum: '',
 *     },
 *   ];
 *   const result = applyColumnFilters(row, filters); // true
 */
export const applyCustomColumnFilters = (
  row: Record<string, unknown>,
  filters: ColumnFilters[],
  specialColumnHandlers?: Record<string, SpecialColumnHandler>,
): boolean => {
  const parseDate = (dateStr: string): moment.Moment | null => {
    const formats = [
      DEFAULT_DATE_VARIANT,
      DEFAULT_DATE_TIME_VARIANT,
      'DD.MM.YYYY HH:mm:ss',
      'DD.MM.YYYY',
      'YYYY-MM-DD HH:mm:ss',
      'YYYY-MM-DD',
      moment.ISO_8601,
    ];
    const parsed = moment(dateStr, formats, true);
    return parsed.isValid() ? parsed.startOf('day') : null;
  };

  return filters.every((filter) => {
    const cellValue = row[filter.column];
    if (cellValue === undefined) return true;

    // Проверяем специальный обработчик
    if (specialColumnHandlers?.[filter.column]) {
      return specialColumnHandlers[filter.column](String(cellValue), filter);
    }

    // Обработка фильтра по списку значений
    if (filter.values.length > 0) {
      return filter.values.includes(String(cellValue));
    }

    // Обработка текстового поиска
    if (filter.query) {
      const stringValue = String(cellValue).toLowerCase();
      return stringValue.includes(filter.query.toLowerCase());
    }

    // Обработка числового диапазона
    if (filter.fromNum || filter.toNum) {
      const numValue = Number(cellValue);
      if (Number.isNaN(numValue)) return true;
      const fromNum = filter.fromNum ? Number(filter.fromNum) : -Infinity;
      const toNum = filter.toNum ? Number(filter.toNum) : Infinity;
      return numValue >= fromNum && numValue <= toNum;
    }

    // Обработка диапазона дат
    if (filter.from || filter.to) {
      const dateValue = parseDate(String(cellValue));
      if (!dateValue) return true; // Если не удалось распарсить дату, пропускаем фильтрацию

      const fromDate = filter.from ? parseDate(filter.from) : moment(0);
      const toDate = filter.to ? parseDate(filter.to) : moment('9999-12-31');

      if (!fromDate || !toDate) return true; // Если не удалось распарсить границы диапазона

      return (
        dateValue.isSameOrAfter(fromDate) && dateValue.isSameOrBefore(toDate)
      );
    }

    return true;
  });
};
