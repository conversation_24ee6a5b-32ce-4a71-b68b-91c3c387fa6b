import { Button, notification, Space, Table, Upload } from 'antd';
import { FC, useEffect, useState } from 'react';
import {
  permissionModalsConfig,
  permissionModalsStore,
} from 'widgets/PermissionModals';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { ApiContainer } from 'shared/ui/ApiContainer';
import { renderGroupAndRolesColumn } from '../../lib';
import {
  useButtonsState,
  useOpenConfirmWithAction,
  useControlExcelActions,
} from '../../model';
import { selectors } from '../../store';

export const RolesTab: FC = () => {
  const [selected, { handleSelection, clearSelection }] = useTableSelection();
  const isButtonDisabled = useButtonsState(selected.rows, 'isDeleted');
  const [roleForCopy, setRoleForCopy] = useState<TableRowData | null>(null);
  const [, open] = permissionModalsStore.hooks.usePermissionModal('role');
  const [handleRoleExport, handleRoleImport] = useControlExcelActions();
  const { createRole, copyRole } =
    permissionModalsStore.hooks.useCreateCopyUpdateRole(clearSelection);

  const { isPending, rows, columns, error, isLoaded } = useAppSelector(
    selectors.rolesTabSelector,
  );

  const isNotOneRoleSelected = selected.rows.length !== 1;

  const dispatch = useAppDispatch();

  const { openModal } = useCreateSliceActions(
    permissionModalsStore.reducers.slice.actions,
  );

  const onRefresh = (): void => {
    dispatch(permissionModalsStore.actions.getRolesThunk());
  };

  /* Хук на создание колбека с пендингом на рефетч */
  const [handleOpenConfirmWithAction, triggerPending] =
    useOpenConfirmWithAction(onRefresh, clearSelection);

  const additionalButtons: AdditionalButton[] = [
    {
      title: 'Назначение "многие ко многим" ',
      key: 'global',
      type: 'primary',
      onClick: () =>
        openModal(permissionModalsConfig.enums.PermissionModals.GlobalAssign),
    },

    {
      title: 'Обновить',
      key: 'refresh',
      type: 'primary',
      disabled: isPending,
      onClick: onRefresh,
    },

    {
      title: 'Новая корневая роль',
      key: 'new-root-role',
      type: 'primary',
      onClick: () => createRole(true, selected.rows[0]),
    },

    {
      disabled: isNotOneRoleSelected,
      title: 'Новая роль',
      key: 'new-role',
      type: 'primary',
      onClick: () => createRole(false, selected.rows[0]),
    },

    {
      disabled: isButtonDisabled.reset,
      title: 'Восстановить',
      key: 'reset-selected',
      type: 'primary',
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.roles.restore,
          {
            title: 'Восстановление',
            subTitle: 'Вы действительно хотите восстановить выбранные роли?',
            notice: 'Роли успешно восстановлены!',
          },
          selected.rows,
        ),
    },

    {
      disabled: isButtonDisabled.delete,
      title: 'Удалить',
      key: 'delete-selected',
      danger: true,
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.roles.delete,
          {
            title: 'Удаление',
            subTitle: 'Вы действительно хотите удалить выбранные роли?',
            notice: 'Роли успешно удалены!',
          },
          selected.rows,
        ),
    },
  ];

  const secondRowButtons: AdditionalButton[] = [
    /* Активна только если выбран 1 чекбокс */
    {
      disabled: isNotOneRoleSelected,
      title: 'Копировать роль и ее права',
      type: 'primary',
      key: 'copy',

      onClick: () => {
        if (selected.rows.length > 0) {
          setRoleForCopy(selected.rows[0]);

          clearSelection();

          notification.success({
            message: `Роль "${selected.rows[0].tabTitle}" скопирована!`,
          });
        }
      },
    },

    /* Активна только если выбран 1 чекбокс и не скопированная роль, включая вложенные роли */
    {
      disabled: Boolean(
        roleForCopy === null ||
          isNotOneRoleSelected ||
          (roleForCopy.key as string).startsWith(
            selected.rows[0].key as string,
          ),
      ),
      title: 'Вставить под текущую',
      type: 'primary',
      key: 'pasteDownCurrent',
      onClick: () => copyRole(false, selected.rows[0], roleForCopy),
    },

    {
      disabled: roleForCopy === null,
      title: 'Вставить как корневую',
      type: 'primary',
      key: 'pasteAsRoot',
      onClick: () => copyRole(true, selected.rows[0], roleForCopy),
    },
  ];

  useEffect(() => {
    if (!isLoaded) {
      onRefresh();
    }
  }, []); // eslint-disable-line

  return (
    <ApiContainer
      error={error}
      isPending={!isLoaded && isPending}
      refresh={onRefresh}
      errorStatus={404}
    >
      <DataGrid
        columns={renderGroupAndRolesColumn(columns)}
        rows={rows}
        searchProps={{
          show: true,
          label:
            'Введите значение для поиска, например: нет ролей или полный доступ',
        }}
        paginationProps={{
          show: false,
          total: 0,
          currentPage: 0,
        }}
        hideColumnSearch
        filterCheckbox={permissionModalsConfig.rolesFilterCheckboxes}
        additionalButtons={additionalButtons}
        additionalComponent={
          <Space>
            <Button
              type="primary"
              ghost
              disabled={isNotOneRoleSelected}
              onClick={() => {
                const roleId = selected.rows?.[0]?.rowId?.rowId;

                if (roleId) {
                  handleRoleExport(roleId);
                }
              }}
            >
              Выгрузить права роли в excel шаблон
            </Button>

            <Upload
              accept=".xlsx"
              showUploadList={false}
              beforeUpload={async (file) => {
                const roleId = selected.rows?.[0]?.rowId?.rowId;

                if (roleId) {
                  await handleRoleImport(file, roleId);
                  onRefresh();
                }

                return false;
              }}
            >
              <Button type="primary" ghost disabled={isNotOneRoleSelected}>
                Загрузить права роли из excel шаблона
              </Button>
            </Upload>
          </Space>
        }
        secondRowAdditionalButtons={secondRowButtons}
        tableAdditionProps={{
          bordered: true,
          loading: {
            size: 'large',
            spinning: triggerPending || (isLoaded && isPending),
          },
          size: 'small',
          scroll: {
            y: `calc(60vh - 39px)`,
            x: '100%',
          },
          pagination: {
            pageSize: 13,
            showSizeChanger: false,
            hideOnSinglePage: true,
            position: ['bottomLeft'],
            size: 'small',
          },
          rowSelection: {
            selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
            columnWidth: 30,
            selectedRowKeys: selected.keys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleSelection({ keys: selectedRowKeys, rows: selectedRows });
            },
          },
        }}
        tableAdditionHandlers={{
          onRowClick: (row) => open(row.rowId?.rowId ?? ''),
        }}
      />
    </ApiContainer>
  );
};
