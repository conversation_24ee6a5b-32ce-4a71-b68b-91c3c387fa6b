import moment from 'moment/moment';
import { useCallback, useLayoutEffect } from 'react';
import { ReplicatorDate } from 'pages/Replicator';
import { apiUrls } from 'shared/api';
import { REPLICATOR_DATE_VARIANT } from 'shared/config/constants';
import { generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

export const useGetData = (): [
  typeof replicatorStatuses,
  (value: string) => void,
] => {
  const [trigger, replicatorStatuses] = useAxiosRequest<ReplicatorDate[]>();

  const getData = useCallback(
    async (selectedDate: string) => {
      await trigger(
        generateUrlWithQueryParams(apiUrls.replicator.data, {
          selectedDate,
        }),
      );
    },
    [trigger],
  );

  useLayoutEffect(() => {
    getData(moment().format(REPLICATOR_DATE_VARIANT));
  }, []); // eslint-disable-line

  return [replicatorStatuses, getData];
};
