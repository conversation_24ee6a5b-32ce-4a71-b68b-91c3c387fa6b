import { Table, Tooltip } from 'antd';
import classNames from 'classnames';
import { FC, useCallback, useEffect } from 'react';
import {
  permissionModalsConfig,
  permissionModalsStore,
} from 'widgets/PermissionModals';
import { DataGrid } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';
import { ApiContainer } from 'shared/ui/ApiContainer';
import { useButtonsState, useOpenConfirmWithAction } from '../../model';
import { selectors } from '../../store';

import styles from './styles.module.scss';
import { UserModals } from './userModals';

export const UsersTab: FC = () => {
  const { isPending, rows, columns, error, isLoaded } = useAppSelector(
    selectors.usersTabSelector,
  );

  const [selected, { handleSelection, clearSelection }] = useTableSelection();
  const isButtonDisabled = useButtonsState(selected.rows, 'isDisabled');
  const [, open] = permissionModalsStore.hooks.usePermissionModal('user');
  const dispatch = useAppDispatch();
  const { openModal } = useCreateSliceActions(
    permissionModalsStore.reducers.slice.actions,
  );

  const onRefresh = useCallback((): void => {
    dispatch(permissionModalsStore.actions.getUsersThunk());
  }, [dispatch]);

  /* Хук на создание колбека с пендингом на рефетч */
  const [handleOpenConfirmWithAction, triggerPending] =
    useOpenConfirmWithAction(onRefresh, clearSelection);

  const additionalButtons: AdditionalButton[] = [
    {
      title: 'Назначение "многие ко многим" ',
      key: 'global',
      type: 'primary',
      onClick: () =>
        openModal(permissionModalsConfig.enums.PermissionModals.GlobalAssign),
    },

    {
      title: 'Обновить',
      key: 'refresh',
      type: 'primary',
      disabled: isPending,
      onClick: onRefresh,
    },

    {
      disabled: isButtonDisabled.reset,
      title: 'Активировать',
      key: 'reset-selected',
      type: 'primary',
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.users.activate,
          {
            title: 'Активация',
            subTitle:
              'Вы действительно хотите активировать выбранных пользователей?',
            notice: 'Пользователи успешно активированы!',
          },
          selected.rows,
        ),
    },
    {
      disabled: isButtonDisabled.delete,
      title: 'Деактивировать',
      key: 'delete-selected',
      danger: true,
      onClick: () =>
        handleOpenConfirmWithAction(
          apiUrls.permission.users.deActivate,
          {
            title: 'Деактивация',
            subTitle:
              'Вы действительно хотите деактивировать выбранных пользователей?',
            notice: 'Пользователи успешно деактивированы!',
          },
          selected.rows,
        ),
    },
    {
      title: 'Настроить профиль по умолчанию',
      key: 'default-profile',
      type: 'primary',
      onClick: () =>
        openModal(permissionModalsConfig.enums.PermissionModals.Profile),
    },
  ];

  useEffect(() => {
    if (!isLoaded) {
      onRefresh();
    }

    dispatch(
      permissionModalsStore.actions.getProfileDataWithDefaultThunk({
        isDefault: true,
      }),
    );
  }, []); // eslint-disable-line

  return (
    <ApiContainer
      error={error}
      isPending={!isLoaded && isPending}
      refresh={onRefresh}
      errorStatus={404}
    >
      <DataGrid
        columns={columns}
        rows={rows}
        searchProps={{
          show: true,
          label:
            'Введите значение для поиска, например: нет ролей или полный доступ',
        }}
        paginationProps={{
          show: false,
          total: 0,
          currentPage: 0,
        }}
        filterCheckbox={permissionModalsConfig.usersFilterCheckboxes}
        resizableProps={{ isActive: true }}
        additionalButtons={additionalButtons}
        columnsProps={{
          render: (text, row) => (
            <Tooltip title={text} placement="topLeft">
              <span
                className={classNames({
                  [styles.cellTextDisabled]: row?.checkboxStatus?.isDisabled,
                  [styles.cellTextDeleted]: row?.checkboxStatus?.isDeleted,
                  [styles.cellTextBlocked]: row?.checkboxStatus?.isBlocked,
                })}
              >
                {text}
              </span>
            </Tooltip>
          ),
        }}
        tableAdditionProps={{
          loading: {
            size: 'large',
            spinning: triggerPending || (isLoaded && isPending),
          },
          tableLayout: 'fixed',
          size: 'small',
          scroll: {
            y: `calc(88vh - 10em)`,
            x: 'max-content',
          },
          pagination: {
            pageSize: 14,
            showSizeChanger: false,
            hideOnSinglePage: true,
            position: ['bottomLeft'],
            size: 'small',
          },
          rowSelection: {
            selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
            selectedRowKeys: selected.keys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleSelection({ keys: selectedRowKeys, rows: selectedRows });
            },
          },
        }}
        tableAdditionHandlers={{
          onRowClick: (row) => open(row.rowId?.rowId ?? ''),
        }}
      />

      <UserModals refresh={onRefresh} />
    </ApiContainer>
  );
};
