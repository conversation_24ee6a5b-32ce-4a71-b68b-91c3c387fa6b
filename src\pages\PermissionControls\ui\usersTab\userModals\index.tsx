import { FC } from 'react';
import { permissionModalsStore } from 'widgets/PermissionModals';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { useTableSelection } from 'shared/model/useTableSelection';

import { AppPopup, ButtonsContainer } from 'shared/ui';
import { useOpenConfirmWithAction } from '../../../model';
import { selectors, reducers } from '../../../store';
import styles from './styles.module.scss';

const UserModal: FC<{
  confirmOptions: {
    subTitle: string;
  };
  isOpened: boolean;
  onClose: Callback;
  refresh: Callback;
  rows: TableRowData[];
  title: string;
  isActivate?: boolean;
}> = ({
  onClose,
  title,
  isOpened,
  refresh,
  confirmOptions,
  isActivate,
  rows,
}) => {
  const [handleOpenConfirmWithAction, isPending] =
    useOpenConfirmWithAction(refresh);
  const [selected, { handleSelection }] = useTableSelection();
  const { columns } = useAppSelector(selectors.usersTabSelector);

  return (
    <AppPopup
      isOpened={isOpened}
      onClose={onClose}
      title={`Внимание! ${title}`}
      className={styles.popup}
    >
      <DataGrid
        columns={columns}
        rows={rows}
        hideSorter
        hideColumnSearch
        tableAdditionProps={{
          rowSelection: {
            selectedRowKeys: selected.keys,
            onChange: (selectedRowKeys, selectedRows) => {
              handleSelection({ keys: selectedRowKeys, rows: selectedRows });
            },
          },
          size: 'small',
          scroll: { x: '100%', y: 300 },
        }}
      />
      <ButtonsContainer
        className={styles.buttons}
        buttons={[
          {
            type: 'primary',
            danger: true,
            htmlType: 'submit',
            disabled: selected.rows.length === 0 || isPending,
            title: isActivate ? 'Активировать' : 'Деактивировать',
            key: 'save',
            onClick: () =>
              handleOpenConfirmWithAction(
                apiUrls.permission.users[
                  isActivate ? 'activate' : 'deActivate'
                ],
                {
                  title: isActivate ? 'Активация' : 'Деактивация',
                  subTitle: confirmOptions.subTitle,
                  notice: `Пользователи успешно ${
                    isActivate ? 'активированы' : 'деактивированы'
                  }!`,
                },
                selected.rows,
              ).then(onClose),
          },
          {
            type: 'primary',
            ghost: true,
            onClick: onClose,
            disabled: isPending,
            title: 'Отмена',
            key: 'abort',
          },
        ]}
      />
    </AppPopup>
  );
};

export const UserModals: FC<{ refresh: Callback }> = ({ refresh }) => {
  const { deletedUserRows, expiredUserRows, lockedUserRows, newUserRows } =
    useAppSelector(selectors.alertPopupsSelector);
  const [userValue] = permissionModalsStore.hooks.usePermissionModal('user');
  const { clearAlertPopup } = useCreateSliceActions(reducers.slice.actions);

  if (userValue !== '') {
    return null;
  }

  const closeNewUsersModal = (): void => clearAlertPopup('newUserRows');
  const closeDeletedUsersModal = (): void => clearAlertPopup('deletedUserRows');
  const closeLockedUsersModal = (): void => clearAlertPopup('lockedUserRows');
  const closeExpiredUsersModal = (): void => clearAlertPopup('expiredUserRows');

  return (
    <>
      <UserModal
        confirmOptions={{
          subTitle:
            'Вы действительно хотите деактивировать выбранных удаленных пользователей?',
        }}
        isOpened={deletedUserRows.length > 0}
        onClose={closeDeletedUsersModal}
        refresh={refresh}
        rows={deletedUserRows}
        title={`Найдено удаленных пользователей: ${deletedUserRows.length}`}
      />

      <UserModal
        confirmOptions={{
          subTitle:
            'Вы действительно хотите деактивировать выбранных пользователей с истекшим сроком действия?',
        }}
        isOpened={expiredUserRows.length > 0}
        onClose={closeExpiredUsersModal}
        refresh={refresh}
        rows={expiredUserRows}
        title={`Найдено пользователей с истекшим сроком учетной записи: ${expiredUserRows.length}`}
      />

      <UserModal
        confirmOptions={{
          subTitle:
            'Вы действительно хотите деактивировать выбранных заблокированных пользователей?',
        }}
        isOpened={lockedUserRows.length > 0}
        onClose={closeLockedUsersModal}
        refresh={refresh}
        rows={lockedUserRows}
        title={`Найдено заблокированных пользователей: ${lockedUserRows.length}`}
      />

      <UserModal
        confirmOptions={{
          subTitle:
            'Вы действительно хотите активировать новых выбранных пользователей?',
        }}
        isActivate
        isOpened={newUserRows.length > 0}
        onClose={closeNewUsersModal}
        refresh={refresh}
        rows={newUserRows}
        title={`Найдено новых пользователей: ${newUserRows.length}`}
      />
    </>
  );
};
