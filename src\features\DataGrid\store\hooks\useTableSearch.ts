import { useCallback, useEffect, useState } from 'react';
import type { TableRowData } from 'features/DataGrid';
import { DataGridProps, TableColumnData, dataGridLib } from 'features/DataGrid';

const { escapeRegExp } = dataGridLib;

type UseTableSearch = (
  rows: TableRowData[],
  columns: TableColumnData[],
  onFilterCallback?: NonNullable<DataGridProps['searchProps']>['onInput'],
  setPage?: (page: number) => void,
) => [
  TableRowData[],
  readonly Key[],
  (filterQuery: string) => void,
  boolean,
  (value: boolean) => void,
];

const handleFilterRows = (
  filteredRows: TableRowData[],
  query: string,
  dataIndexes: Set<string>,
  foundedKeysSet: Set<Key>,
  allKeysSet: Set<Key>,
  foundedRows: TableRowData[],
): TableRowData[] =>
  filteredRows.reduce<TableRowData[]>((newRows, currentRow) => {
    const children: TableRowData[] = currentRow.children
      ? handleFilterRows(
          currentRow.children,
          query,
          dataIndexes,
          foundedKeysSet,
          allKeysSet,
          foundedRows,
        )
      : [];

    if (
      Object.entries(currentRow).find(([keyName, cell]) => {
        if (!dataIndexes.has(keyName)) return false;
        if (typeof cell !== 'string') return false;

        let _isRegExpTest: boolean;

        try {
          _isRegExpTest = RegExp(escapeRegExp(query), 'igm').test(
            dataGridLib.deleteExtraSpaces(cell),
          );
        } catch {
          _isRegExpTest = false;
        }

        return _isRegExpTest;
      })
    ) {
      allKeysSet.add(currentRow.key);
      foundedKeysSet.add(currentRow.key);
      foundedRows.push({ ...currentRow, children: undefined });

      newRows.push({
        ...currentRow,
        children: children.length > 0 ? children : undefined,
      });
    } else if (children.length > 0) {
      // Если найдены потомки, то пушит вершину
      newRows.push({ ...currentRow, children });
      allKeysSet.add(currentRow.key);
    }

    return newRows;
  }, []);

const DEFAULT_PAGE = 1;

export const useTableSearch: UseTableSearch = (
  rows,
  columns,
  onFilterCallback,
  setPage,
) => {
  const [searchValue, setSearchValue] = useState('');
  const [filterQuery, setFilterQuery] = useState('');
  const [allKeys, setAllKeys] = useState<readonly Key[]>([]);
  const [allFilteredRows, setAllFilteredRows] = useState<TableRowData[]>([]);
  const [onlyFoundedRows, setOnlyFoundedRows] = useState<TableRowData[]>([]);
  const [isOnlyFoundedShown, setOnlyFoundedShown] = useState(false);

  useEffect(() => {
    if (filterQuery === '') {
      setAllFilteredRows(rows);
      setAllKeys([]);
      return;
    }

    const foundedKeysSet = new Set<Key>();
    const allKeysSet = new Set<Key>();
    const foundedRows: TableRowData[] = [];
    const filteredRows = handleFilterRows(
      rows,
      filterQuery,
      new Set(columns.map((item) => item.dataIndex)),
      foundedKeysSet,
      allKeysSet,
      foundedRows,
    );

    setAllFilteredRows(filteredRows);
    setOnlyFoundedRows(foundedRows);
    setAllKeys([...allKeysSet]);
  }, [filterQuery, rows, columns]);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (onFilterCallback) {
        onFilterCallback(searchValue);
      }
      setPage?.(DEFAULT_PAGE);
      setFilterQuery(searchValue);
    }, 900);

    if (searchValue.trim() === '') {
      setOnlyFoundedShown(false);
    }

    return () => {
      clearTimeout(handler);
    };
  }, [searchValue]); // eslint-disable-line

  const handleFilterQuery = useCallback(
    (value: string) => setSearchValue(dataGridLib.deleteExtraSpaces(value)),
    [setSearchValue],
  );
  const handleOnlyFounded = useCallback(setOnlyFoundedShown, [
    setOnlyFoundedShown,
  ]);

  return [
    isOnlyFoundedShown ? onlyFoundedRows : allFilteredRows,
    allKeys,
    handleFilterQuery,
    isOnlyFoundedShown,
    handleOnlyFounded,
  ];
};
