import { Table } from 'antd';
import { FC, useState, useCallback, useMemo } from 'react';
import { useEffectOnce } from 'react-use';

import { TableColumnData, TableRowData } from 'features/DataGrid';
import { FilenetActions } from 'entities/FilenetActions';
import { FilenetResponseData } from 'entities/FilenetAttachmentsTree';
import { apiUrls, filenetServiceInstance } from 'shared/api';
import { errorMessages } from 'shared/config';
import { appErrorNotification } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';
import { ApiContainer } from 'shared/ui';

import styles from './styles.module.scss';

const tabsColumns: TableColumnData[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 0.6,
  },
  {
    title: 'Наименование файла',
    dataIndex: 'title',
    key: 'title',
    width: 2,
  },
  {
    title: 'Размер',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 1,
  },
  {
    title: 'Изменен',
    dataIndex: 'modifiedGibrDatetime',
    key: 'modifiedGibrDatetime',
    width: 1.8,
  },
  {
    title: 'Загружен в ИВС ГИБР',
    dataIndex: 'uploadGibrDatetime',
    key: 'uploadGibrDatetime',
    width: 1.8,
  },
  {
    title: 'Загружен в КЗ ИД',
    dataIndex: 'uploadDatetime',
    key: 'uploadDatetime',
    width: 1.8,
  },
  {
    title: 'Тип файла',
    dataIndex: 'documentTypeExternalId',
    key: 'documentTypeExternalId',
    width: 1,
    render: (text) => (
      <div className={styles.cell}>
        {text}
        {/* &nbsp; */}
        {/* TODO: понять что за кнопка */}
        {/* <Popover content={<div />} trigger="click" placement="bottom">*/}
        {/*  <Button type="primary" size="small">*/}
        {/*    ...*/}
        {/*  </Button>*/}
        {/* </Popover>*/}
      </div>
    ),
  },
  {
    title: 'Действия',
    dataIndex: 'actions',
    key: 'actions',
    width: 1.5,
    render: (_id, row) => (
      <FilenetActions
        permissions={row.viewDownloadPermissions ?? []}
        cardId={row.id}
        attachment={(() => {
          const paths = String(row.title).split('/');

          return {
            id: row.attachmentId,
            filename: String(paths.at(-1)),
          };
        })()}
        size="small"
      />
    ),
    hideSorter: true,
    hideColumnSearch: true,
  },
];

interface AssDocsResponse {
  isLastPage: boolean;
  items: FilenetResponseData[];
}

const PAGE_SIZE_OPTIONS = ['5', '10', '20', '50', '100'];

export const AssDocTab: FC<{
  popupId: string | number;
  tabKey: string;
}> = ({ popupId, tabKey }) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [total, setTotal] = useState(0);

  const [trigger, { isPending, error, data }] =
    useAxiosRequest<AssDocsResponse>(filenetServiceInstance);

  const rows: TableRowData[] = useMemo(
    () =>
      data
        ? data?.items?.map((item) => ({
            ...item,
            key: item.id as number,
          }))
        : [],
    [data],
  );

  const fetchData = useCallback(
    async (pageNumber: number = page, size: number = pageSize) => {
      const res = await trigger(apiUrls.fileNet.assDocsFiles, {
        method: 'POST',
        data: {
          ids: [popupId],
          groupExternalId: tabKey,
          offset: (pageNumber - 1) * size,
          size,
        },
      }).catch((err) => {
        appErrorNotification(
          'Произошла ошибка при загрузке приклепленных файлов',
          err as AppError,
        );
        throw err;
      });
      if (res) {
        const newTotal = res.isLastPage
          ? (pageNumber - 1) * size + (res?.items?.length ?? 0)
          : (pageNumber + 1) * size;
        setTotal(newTotal);
      }
    },
    [page, pageSize, trigger, popupId, tabKey],
  );

  const handlePageChange = async (
    newPage: number,
    newPageSize?: number,
  ): Promise<void> => {
    try {
      if (newPageSize && newPageSize !== pageSize) {
        // Сохраняем текущий offset
        const offset = (page - 1) * pageSize;
        const recalculatedPage = Math.floor(offset / newPageSize) + 1;
        setPageSize(newPageSize);
        setPage(recalculatedPage);
        await fetchData(recalculatedPage, newPageSize);
      } else {
        await fetchData(newPage, pageSize);
        setPage(newPage);
      }
    } catch (err) {
      appErrorNotification(
        'Произошла ошибка при загрузке приклепленных файлов',
        err as AppError,
      );
    }
  };

  // Загрузка при монтировании
  useEffectOnce(() => {
    fetchData();
  });

  return (
    <ApiContainer
      error={error}
      isPending={false}
      errorTitle={errorMessages.pendingError}
    >
      <Table
        loading={isPending}
        pagination={{
          current: page,
          total,
          pageSize: pageSize,
          showSizeChanger: true,
          pageSizeOptions: PAGE_SIZE_OPTIONS,
          onChange: handlePageChange,
        }}
        columns={tabsColumns}
        dataSource={rows}
      />
    </ApiContainer>
  );
};

export default AssDocTab;
