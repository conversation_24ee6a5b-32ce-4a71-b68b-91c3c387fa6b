import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Button, Select, Typography } from 'antd';
import moment, { Moment } from 'moment/moment';
import { FC, ReactElement } from 'react';
import { ReplicatorConfig } from 'pages/Replicator';
import { ReplicatorSettingsStore } from 'widgets/ReplicatorSettings';
import { apiUrls } from 'shared/api';
import { REPLICATOR_DATE_VARIANT } from 'shared/config/constants';
import { useAppDispatch } from 'shared/model';
import { ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

interface HeaderProps<DateType> {
  getData: (value: string) => void;
  handleNowDate: (value: Moment) => void;
  handlePopup: (name: keyof typeof ReplicatorConfig.popupInitial) => void;
  nowDate: Moment;
  onChange: (date: DateType) => void;
  value: Moment;
}
export const Header: FC<HeaderProps<Moment>> = ({
  value,
  onChange,
  handlePopup,
  handleNowDate,
  nowDate,
  getData,
}) => {
  const dispatch = useAppDispatch();
  const month = value.month();
  const year = value.year();
  const currentYearFromCalendar = moment().year();

  const months = [...Array(12)].map((_, index) =>
    value.localeData().monthsShort(value.clone().month(index)),
  );

  const monthOptions = [...Array(12).keys()].map((key) => (
    <Select.Option className="month-item" key={key}>
      {months[key]}
    </Select.Option>
  ));

  const yearOptions = [...Array(20).keys()].map((key) => (
    <Select.Option
      className="year-item"
      key={key}
      value={currentYearFromCalendar - key}
    >
      {currentYearFromCalendar - key}
    </Select.Option>
  ));

  return (
    <div className={styles.calendarHeader}>
      <ButtonsContainer
        buttons={[
          {
            title: 'Настройки',
            key: 'settings',
            onClick: () => {
              dispatch(
                ReplicatorSettingsStore.actions.getParamsThunk(
                  apiUrls.replicator.settings.getSettings,
                ),
              );
              handlePopup('settings');
            },
            type: 'primary',
            tooltip: 'Нажмите для настройки загрузки',
          },
          {
            title: 'Предыдущий месяц',
            key: 'prev',
            onClick: () => {
              handleNowDate(nowDate.add(-1, 'months'));
              getData(nowDate.format(REPLICATOR_DATE_VARIANT));
              if (onChange) {
                onChange(nowDate);
              }
            },
            icon: <LeftOutlined rev="" />,
          },
          {
            title: 'Следующий месяц',
            key: 'next',
            onClick: () => {
              handleNowDate(nowDate.add(1, 'months'));
              getData(nowDate.format(REPLICATOR_DATE_VARIANT));
              if (onChange) {
                onChange(nowDate);
              }
            },
            icon: <RightOutlined rev="" />,
          },
        ]}
      />

      <Button onClick={() => handlePopup('upload')}>
        Загрузка справочника САДД
      </Button>

      <div className={styles.calendarHeaderSelect}>
        <div className={styles.calendarHeaderSelectBox}>
          <Typography.Text className={styles.calendarHeaderSelectBoxText}>
            Выберите месяц:
          </Typography.Text>
          <Select
            className={styles.calendarHeaderSelectBoxAction}
            size="small"
            dropdownMatchSelectWidth={false}
            defaultValue={String(month)}
            value={String(month)}
            onChange={(selectedMonth: string) => {
              const newValue = value.clone();
              newValue.month(parseInt(selectedMonth, 10));
              handleNowDate(newValue);
              getData(newValue.format(REPLICATOR_DATE_VARIANT));
              if (onChange) {
                onChange(newValue);
              }
            }}
          >
            {monthOptions}
          </Select>
        </div>

        <div className={styles.calendarHeaderSelectBox}>
          <Typography.Text className={styles.calendarHeaderSelectBoxText}>
            Выберите год:
          </Typography.Text>
          <Select
            className={styles.calendarHeaderSelectBoxAction}
            size="small"
            dropdownMatchSelectWidth={false}
            onChange={(newYear: string) => {
              const now = value.clone().year(Number(newYear));
              handleNowDate(now);
              getData(now.format(REPLICATOR_DATE_VARIANT));
              if (onChange) {
                onChange(now);
              }
            }}
            value={String(year)}
            defaultValue={String(year)}
          >
            {yearOptions}
          </Select>
        </div>
      </div>
    </div>
  );
};

export const calendarHeader = (props: HeaderProps<Moment>): ReactElement => (
  <Header {...props} />
);
