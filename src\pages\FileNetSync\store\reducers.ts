import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import moment from 'moment';
import type { TableRowData } from 'features/DataGrid';
import { DEFAULT_DATE_VARIANT } from 'shared/config/constants';
import type { FileNetSyncInitialState } from '..';
import {
  downloadDBReportThunk,
  getRepositoriesThunk,
  getRepositorySyncThunk,
  getSummaryThunk,
  postUploadLogThunk,
} from './actions';
import { Detalization, ImportStatus } from './enums';

const initialState: FileNetSyncInitialState = {
  modals: {
    isDiskOpen: false,
    isFormOpen: false,
  },
  repositorySync: {
    isPending: false,
    error: null,
    repos: [],
  },
  repositories: {
    isPending: false,
    error: null,
    repos: [],
  },
  summary: {
    isPending: false,
    error: null,
    replicatorRepositoriesStatusInfo: {
      repositories: [],
    },
    uploadRepositoryInfo: {
      repositories: [],
    },
    componentAvailabilityInfo: {
      summaryStatus: '',
      components: [],
    },
  },
  uploadLog: {
    api: {
      isPending: false,
      error: null,
      commits: [],
      page: 1,
      totalRecords: 0,
      rows: [],
      columns: [
        {
          key: 'datetime',
          title: 'Дата',
          dataIndex: 'datetime',
        },
        {
          key: 'repository',
          title: 'Репозиторий',
          dataIndex: 'repository',
        },
        {
          key: 'commit',
          title: 'Идентификатор',
          dataIndex: 'commit',
        },
        {
          key: 'statusLabel',
          title: 'Статус',
          dataIndex: 'statusLabel',
        },
      ],
    },
    formData: {
      detalization: Detalization.ALL,
      importStatus: ImportStatus.ALL,
      periodFrom: moment().subtract(7, 'days').format(DEFAULT_DATE_VARIANT),
      periodTo: moment().format(DEFAULT_DATE_VARIANT),
      title: '',
      ui: {
        filters: null,
        first: 0,
        rows: 10,
        sortField: 'actionDatetime',
        sortOrder: '-1',
      },
      repositories: [],
    },
  },
};

export const slice = createSlice({
  name: 'fileNetSync',
  initialState,
  reducers: {
    handleModalState: (
      state,
      {
        payload: { status, popup },
      }: PayloadAction<{
        popup: keyof FileNetSyncInitialState['modals'];
        status: boolean;
      }>,
    ) => {
      state.modals[popup] = status;
    },

    handleFormDataClear: (state) => {
      state.uploadLog.formData = { ...initialState.uploadLog.formData };
    },
    setDetalization: (state, { payload }: PayloadAction<Detalization>) => {
      state.uploadLog.formData.detalization = payload;
    },
    setImportStatus: (state, { payload }: PayloadAction<ImportStatus>) => {
      state.uploadLog.formData.importStatus = payload;
    },
    setInputValues: (
      state,
      {
        payload: { input, value },
      }: PayloadAction<{
        input: Extract<
          keyof typeof initialState.uploadLog.formData,
          'periodFrom' | 'periodTo' | 'title'
        >;
        value: string;
      }>,
    ) => {
      state.uploadLog.formData[input] = value;
    },

    setRepositories: (state, { payload }: PayloadAction<string[]>) => {
      state.uploadLog.formData.repositories = payload;
    },

    setPage: (state, { payload }: PayloadAction<number>) => {
      const page = payload <= 0 ? 1 : payload;

      state.uploadLog.api.page = page;
      state.uploadLog.formData.ui.first =
        (page - 1) * state.uploadLog.formData.ui.rows;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(postUploadLogThunk.pending, (state) => {
      state.uploadLog.api.isPending = true;
      state.uploadLog.api.error = null;
    });
    builder.addCase(postUploadLogThunk.fulfilled, (state, { payload }) => {
      state.uploadLog.api.isPending = false;
      state.uploadLog.api.commits = payload.data;
      state.uploadLog.api.rows = payload.data.map((data) => ({
        ...data,
        key: data.commit,
      })) as unknown as TableRowData[];
      state.uploadLog.api.totalRecords = payload.totalRecords;
    });
    builder.addCase(postUploadLogThunk.rejected, (state, action) => {
      state.uploadLog.api.error = action.error;
      state.uploadLog.api.isPending = false;
    });

    builder.addCase(getRepositoriesThunk.pending, (state) => {
      state.repositories.isPending = true;
      state.repositories.error = null;
    });
    builder.addCase(getRepositoriesThunk.fulfilled, (state, { payload }) => {
      state.repositories.isPending = false;
      state.repositories.repos = payload.data;
    });
    builder.addCase(getRepositoriesThunk.rejected, (state, action) => {
      state.repositories.isPending = false;
      state.repositories.error = action.error;
    });

    builder.addCase(downloadDBReportThunk.pending, (state) => {
      state.uploadLog.api.isPending = true;
    });
    builder.addCase(downloadDBReportThunk.rejected, (state) => {
      state.uploadLog.api.isPending = false;
    });
    builder.addCase(downloadDBReportThunk.fulfilled, (state) => {
      state.uploadLog.api.isPending = false;
    });

    builder.addCase(getSummaryThunk.pending, (state) => {
      state.summary.isPending = true;
      state.summary.error = null;
    });
    builder.addCase(getSummaryThunk.fulfilled, (state, { payload }) => {
      state.summary = { ...state.summary, ...payload };
      state.summary.isPending = false;
    });
    builder.addCase(getSummaryThunk.rejected, (state, action) => {
      state.summary.isPending = false;
      state.summary.error = action.error;
    });

    builder.addCase(getRepositorySyncThunk.pending, (state) => {
      state.repositorySync.isPending = true;
      state.repositorySync.error = null;
    });
    builder.addCase(getRepositorySyncThunk.fulfilled, (state, { payload }) => {
      state.repositorySync.repos = payload;
      state.repositorySync.isPending = false;
    });
    builder.addCase(getRepositorySyncThunk.rejected, (state, action) => {
      state.repositorySync.isPending = false;
      state.repositorySync.error = action.error;
    });
  },
});
